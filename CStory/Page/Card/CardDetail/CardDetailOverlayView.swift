//
//  CardDetailOverlayView.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftData
import SwiftUI

/// 卡片详情遮罩视图
///
/// 用于显示卡片详情或创建新卡片的全屏遮罩视图。
/// 支持多种动画模式，可以从不同界面进入。
///
/// ## 功能特性
/// - 查看和编辑卡片信息
/// - 创建新卡片
/// - 支持多种动画模式
/// - 交易列表和统计信息
/// - 丰富的编辑选项
struct CardDetailOverlayView: View {
  // MARK: - 核心参数
  let cardId: UUID?  // 卡片ID，用于查看现有卡片（新架构）
  let cardNamespace: Namespace.ID?
  @Binding var showingCardDetail: Bool
  let pathManager: PathManagerHelper
  let animationMode: AnimationMode

  // 创建模式参数
  let isCreatingCard: Bool
  let isCredit: Bool?  // 替代 CardType，用于判断是否为信用卡
  let mainCategory: CardCategoryResponse?
  let subCategory: CardSubCategoryResponse?
  let onCardCreated: ((CardModel) -> Void)?

  // MARK: - 兼容性参数（废弃，但保留向后兼容）
  private let legacyCard: CardModel?  // 旧的卡片参数
  private let legacyTransactions: [TransactionModel]  // 旧的交易参数
  private let legacyCurrencies: [CurrencyModel]  // 旧的货币参数

  // MARK: - 环境对象
  @Environment(\.modelContext) private var modelContext
  @Environment(\.dataManager) private var dataManager  // 仅用于配置 ViewModel

  // MARK: - ViewModel
  @ObservedObject private var viewModel: CardDetailOverlayVM

  // MARK: - Focus State
  @FocusState private var isCardNameFocused: Bool

  // MARK: - 动画状态
  @State private var showBackground: Bool = false
  @State private var showCard: Bool = false
  @State private var showDetailContent: Bool = false
  @State private var showCreateForm: Bool = false

  // MARK: - 触觉反馈管理器
  private let hapticManager = HapticFeedbackManager.shared

  /// 定义不同的进入动画模式
  enum AnimationMode {
    case matchedGeometry  // 卡包进入：使用matchedGeometryEffect的展开动画
    case slideFromTop  // 首页进入：从顶部滑入的动画
    case createCard  // 创建进入：专门的创建卡片动画
  }

  // MARK: - 初始化
  /// 兼容性初始化方法（废弃，但保留向后兼容）
  init(
    card: CardModel?,
    cardNamespace: Namespace.ID?,
    showingCardDetail: Binding<Bool>,
    pathManager: PathManagerHelper,
    transactions: [TransactionModel],
    currencies: [CurrencyModel],
    animationMode: AnimationMode,
    isCreatingCard: Bool,
    isCredit: Bool?,
    mainCategory: CardCategoryResponse?,
    subCategory: CardSubCategoryResponse?,
    onCardCreated: ((CardModel) -> Void)?
  ) {
    // 新架构参数
    self.cardId = card?.id
    self.cardNamespace = cardNamespace
    self._showingCardDetail = showingCardDetail
    self.pathManager = pathManager
    self.animationMode = animationMode
    self.isCreatingCard = isCreatingCard
    self.isCredit = isCredit
    self.mainCategory = mainCategory
    self.subCategory = subCategory
    self.onCardCreated = onCardCreated

    // 兼容性参数
    self.legacyCard = card
    self.legacyTransactions = transactions
    self.legacyCurrencies = currencies

    // 初始化ViewModel
    self.viewModel = CardDetailOverlayVM()
  }

  /// 新架构初始化方法（推荐使用）
  init(
    cardId: UUID?,
    cardNamespace: Namespace.ID? = nil,
    showingCardDetail: Binding<Bool>,
    pathManager: PathManagerHelper,
    animationMode: AnimationMode,
    isCreatingCard: Bool = false,
    isCredit: Bool? = nil,
    mainCategory: CardCategoryResponse? = nil,
    subCategory: CardSubCategoryResponse? = nil,
    onCardCreated: ((CardModel) -> Void)? = nil
  ) {
    self.cardId = cardId
    self.cardNamespace = cardNamespace
    self._showingCardDetail = showingCardDetail
    self.pathManager = pathManager
    self.animationMode = animationMode
    self.isCreatingCard = isCreatingCard
    self.isCredit = isCredit
    self.mainCategory = mainCategory
    self.subCategory = subCategory
    self.onCardCreated = onCardCreated

    // 兼容性参数设为空
    self.legacyCard = nil
    self.legacyTransactions = []
    self.legacyCurrencies = []

    // 初始化ViewModel
    self.viewModel = CardDetailOverlayVM()
  }

  // MARK: - 计算属性

  /// 当前卡片数据（统一数据源）
  private var currentCard: CardModel? {
    print("🔍 currentCard computed - cardId: \(cardId?.uuidString ?? "nil")")

    if let cardId = cardId {
      // 新架构：从 DataManager 获取最新数据
      let foundCard = dataManager.findCard(by: cardId)
      print("🔍 Found card from DataManager: \(foundCard?.name ?? "nil")")
      return foundCard
    } else {
      // 兼容性：使用传入的卡片数据
      print("🔍 Using legacy card: \(legacyCard?.name ?? "nil")")
      return legacyCard
    }
  }

  /// 是否为信用卡类型
  private var isCreditCard: Bool {
    viewModel.isCreditCard(isCreatingCard: isCreatingCard, isCredit: isCredit, card: currentCard)
  }

  /// 预览用的CardModel对象，基于编辑状态数据
  private var previewCard: CardModel {
    viewModel.getPreviewCard(
      card: currentCard,
      isCreatingCard: isCreatingCard,
      isCredit: isCredit,
      subCategory: subCategory,
      mainCategory: mainCategory
    )
  }

  // MARK: - 主体视图

  var body: some View {
    ZStack {
      // 背景
      Color.black.opacity(0.3)
        .opacity(showBackground ? 1 : 0)
        .ignoresSafeArea()

      // 主内容
      VStack(spacing: 24) {
        cardView
        detailContentView
      }
      .padding(.top, 16)
      .background(.regularMaterial)
      .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    .onAppear {
      print(
        "🔍 CardDetailOverlayView onAppear - cardId: \(cardId?.uuidString ?? "nil"), isCreatingCard: \(isCreatingCard), animationMode: \(animationMode)"
      )

      // 配置ViewModel依赖
      viewModel.configure(with: dataManager)

      // 直接准备数据，不使用延迟
      prepareDataForDisplay()
      showingCardDetail = true
      setupAnimations()

      // 立即加载最新数据
      if !isCreatingCard {
        loadCardDetailData()
      }
    }
    .onChange(of: legacyTransactions) {
      if showingCardDetail && !isCreatingCard {
        loadCardDetailData()
      }
    }
    // 监听导航路径变化，当从交易详情页面返回时刷新数据
    .onChange(of: pathManager.path) { newPath in
      print("🔍 Path changed - cardId: \(cardId?.uuidString ?? "nil"), path count: \(newPath.count)")
      print("🔍 Current path: \(newPath)")

      if showingCardDetail && !isCreatingCard {
        print("🔍 Will refresh data after path change")
        // 延迟刷新，确保导航动画完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
          loadCardDetailData()
        }
      }
    }

    .floatingSheet(
      isPresented: $viewModel.showingCoverOptions,
      config: SheetBase(
        maxDetent: .height(450),
        cornerRadius: 24,
        interactiveDimiss: false,
        hPadding: 8,
        bPadding: 4
      )
    ) {
      SelectCoverSheet(
        selectedType: $viewModel.editSelectedcoverType,
        isDarkBackground: $viewModel.editisCoverDark
      )
    }

    // 数字键盘弹窗
    .floatingSheet(
      isPresented: $viewModel.showingNumericKeypad,
      config: SheetBase(
        maxDetent: .height(314),
        cornerRadius: 24,
        interactiveDimiss: false,
        hPadding: 8,
        bPadding: 4
      )
    ) {

      NumericKeypad(
        text: $viewModel.tempAmount,
        onSave: {
          viewModel.handleNumericKeypadInput()
        },
        allowNegative: viewModel.keypadAllowsNegative(for: viewModel.editingAmount ?? .balance),
        maxDecimalPlaces: viewModel.keypadMaxDecimalPlaces(for: viewModel.editingAmount ?? .balance)
      )

    }
    // 账单日选择器
    .floatingSheet(
      isPresented: $viewModel.showingBillingDayPicker,
      config: SheetBase(
        maxDetent: .height(300),
        cornerRadius: 24,
        interactiveDimiss: false,
        hPadding: 8,
        bPadding: 4
      )
    ) {
      DayPickerView(
        selectedDay: $viewModel.selectedDay,
        repaymentType: $viewModel.editRepaymentType,
        onSave: {
          viewModel.handleBillingDaySave()
        },
        onClear: {
          viewModel.handleBillingDayClear()
        }
      )

    }
    // 还款日选择器
    .floatingSheet(
      isPresented: $viewModel.showingRepaymentDayPicker,
      config: SheetBase(
        maxDetent: .height(300),
        cornerRadius: 24,
        interactiveDimiss: false,
        hPadding: 8,
        bPadding: 4
      )
    ) {
      DayPickerView(
        selectedDay: $viewModel.selectedDay,
        repaymentType: $viewModel.editRepaymentType,
        onSave: {
          viewModel.handleRepaymentDaySave()
        },
        onClear: {
          viewModel.handleRepaymentDayClear()
        },
        isRepaymentDay: true
      )

    }
    // 货币选择器
    .floatingSheet(
      isPresented: $viewModel.showingCurrencyPicker,
      config: SheetBase(
        maxDetent: .fraction(0.7),
        cornerRadius: 24,
        interactiveDimiss: false,
        hPadding: 8,
        bPadding: 4
      )
    ) {
      SelectCurrencySheet(
        selectedCurrencyCode: $viewModel.editSelectedCurrencyCode,
        currencySymbol: $viewModel.editCurrencySymbol,
        onSelect: { code in
          viewModel.handleCurrencySelection(code: code)
        }
      )
    }
    .alert("提示", isPresented: $viewModel.showingAlert) {
      Button("确定") {}
    } message: {
      Text(viewModel.alertMessage)
    }
    // 卡片操作菜单
    .floatingSheet(
      isPresented: $viewModel.showCardActionSheet,
      config: SheetBase(
        maxDetent: .fraction(0.45),
        cornerRadius: 24,
        interactiveDimiss: false,
        hPadding: 8,
        bPadding: 4
      )
    ) {
      CardActionSheet(
        onEditCard: {
          viewModel.showCardActionSheet = false
          viewModel.toggleCardFlip()
        },
        onDeleteWithTransactions: {
          guard let card = currentCard else { return }
          viewModel.deleteCardWithTransactions(card: card, modelContext: modelContext)
          closeDetailView()
        },
        onDeleteCardOnly: {
          guard let card = currentCard else { return }
          viewModel.deleteCardOnly(card: card, modelContext: modelContext)
          closeDetailView()
        },
        dismiss: { viewModel.showCardActionSheet = false }
      )
    }
  }

  // MARK: - 主要视图组件

  private var detailContentView: some View {
    Group {
      if isCreatingCard {
        // 创建模式：直接显示编辑视图，从底部滑入
        createModeEditingView
      } else {
        // 编辑模式：保持详情↔编辑切换逻辑
        editModeContentView
      }
    }
    .offset(
      y: shouldShowDetailContent ? 0 : UIScreen.main.bounds.height
    )
    .opacity(shouldShowDetailContent ? 1 : 0)
    // 在matchedGeometry模式下使用更简洁的动画，避免干扰
    .animation(
      animationMode == .matchedGeometry
        ? .easeInOut(duration: 0.2)
        : .interactiveSpring(response: 0.25, dampingFraction: 0.8),
      value: showDetailContent
    )
    .animation(
      .interactiveSpring(response: 0.25, dampingFraction: 0.8),
      value: showCreateForm
    )
  }

  /// 创建模式的编辑视图（直接显示，无切换动画）
  private var createModeEditingView: some View {
    ZStack {
      ScrollView {
        VStack(spacing: 16) {
          // 创建模式头部
          HStack {
            Text("创建卡片")
              .font(.system(size: 16, weight: .medium))
              .foregroundColor(.cBlack)
            Spacer()
          }
          .padding(.horizontal, 16)
          .padding(.top, 8)

          editingFormView
          Spacer(minLength: 80)  // 为底部按钮留出空间
        }
      }

      // 底部创建按钮
      createModeBottomButtons
    }
  }

  /// 创建模式底部按钮
  private var createModeBottomButtons: some View {
    FloatingActionButtonView(
      buttons: [
        FloatingActionButton(
          title: "取消",
          action: {
            hapticManager.trigger(.impactLight)
            cancelCardEditing()
          },
          style: .secondary
        ),
        FloatingActionButton(
          title: "创建",
          action: {
            hapticManager.trigger(.impactMedium)
            handleSaveCard()
          },
          style: .primary
        ),
      ]
    )
  }

  /// 编辑模式的内容视图（支持详情↔编辑切换）
  private var editModeContentView: some View {
    ZStack {
      detailInfoView
      editingView
    }
    .animation(.easeInOut(duration: 0.3), value: viewModel.isEditingCard)
  }

  /// 是否应该显示详情内容
  private var shouldShowDetailContent: Bool {
    switch animationMode {
    case .createCard:
      return showCreateForm
    default:
      return showDetailContent
    }
  }

  /// 详情视图底部按钮
  private var detailViewBottomButtons: some View {
    FloatingActionButtonView(
      title: "关闭",
      action: {
        hapticManager.trigger(.impactLight)
        closeDetailView()
      },
      style: .secondary
    )
  }

  // MARK: - 私有方法

  /// 准备显示数据（统一的数据准备入口）
  private func prepareDataForDisplay() {
    // 首先初始化当前卡片数据
    viewModel.initializeCurrentCard(currentCard)

    viewModel.prepareDataForDisplay(
      isCreatingCard: isCreatingCard,
      card: viewModel.currentCard,  // 使用ViewModel中的数据
      baseCurrency: viewModel.baseCurrencies,
      isCredit: isCredit,
      transactions: legacyTransactions,  // 使用兼容性数据，新架构中会被忽略
      currencies: viewModel.allCurrencies,
      selectedPeriod: viewModel.selectedPeriod,
      currentDate: viewModel.currentDate
    )
  }

  /// 设置动画
  private func setupAnimations() {
    switch animationMode {
    case .slideFromTop:
      setupSlideFromTopAnimation()
    case .matchedGeometry:
      setupMatchedGeometryAnimation()
    case .createCard:
      setupCreateCardAnimation()
    }
  }

  /// 首页进入：从顶部滑入动画
  private func setupSlideFromTopAnimation() {
    withAnimation(.easeOut(duration: 0.3)) {
      showBackground = true
    }
    withAnimation(.easeInOut(duration: 0.4)) {
      showCard = true
    }
    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
      withAnimation(.easeInOut(duration: 0.25)) {
        showDetailContent = true
      }
    }
  }

  /// 卡包进入：matchedGeometry展开动画
  private func setupMatchedGeometryAnimation() {
    showBackground = true
    // 更短的延迟，让matchedGeometryEffect先完成
    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
      withAnimation(.easeInOut(duration: 0.2)) {
        showDetailContent = true
      }
    }
  }

  /// 创建模式：专门的创建动画
  private func setupCreateCardAnimation() {
    // 第一阶段：背景渐入
    withAnimation(.easeOut(duration: 0.3)) {
      showBackground = true
    }

    // 第二阶段：延迟后卡片从顶部滑入
    DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
      withAnimation(.easeInOut(duration: 0.4)) {
        showCard = true
      }
    }

    // 第三阶段：卡片动画完成后创建表单从底部滑入（减少延迟时间）
    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
      withAnimation(.easeInOut(duration: 0.25)) {
        showCreateForm = true
      }
    }
  }

  /// 加载卡片详情数据
  private func loadCardDetailData() {
    print("🔍 loadCardDetailData called - cardId: \(cardId?.uuidString ?? "nil")")
    print("🔍 currentCard before init: \(currentCard?.name ?? "nil")")

    // 确保当前卡片数据是最新的
    viewModel.initializeCurrentCard(currentCard)

    print("🔍 viewModel.currentCard after init: \(viewModel.currentCard?.name ?? "nil")")

    // 直接调用 ViewModel 的加载方法，时间参数从时间控制器获取
    viewModel.loadCardDetailData(
      card: viewModel.currentCard,
      selectedPeriod: viewModel.selectedPeriod,
      currentDate: viewModel.currentDate
    )
  }

  /// 关闭详情视图
  private func closeDetailView() {
    switch animationMode {
    case .slideFromTop:
      closeWithSlideAnimation()
    case .matchedGeometry:
      closeWithMatchedGeometryAnimation()
    case .createCard:
      closeWithCreateCardAnimation()
    }
  }

  /// slideFromTop模式的关闭动画
  private func closeWithSlideAnimation() {
    // 第一阶段：详情内容滑向底部
    withAnimation(.easeOut(duration: 0.2)) {
      showDetailContent = false
    }

    // 第二阶段：卡片滑向上方
    DispatchQueue.main.asyncAfter(deadline: .now() + 0.15) {
      withAnimation(.easeOut(duration: 0.3)) {
        showCard = false
      }
    }

    // 第三阶段：背景渐出同时整体视图关闭
    DispatchQueue.main.asyncAfter(deadline: .now() + 0.35) {
      withAnimation(.easeOut(duration: 0.15)) {
        showBackground = false
        showingCardDetail = false
      }
    }
  }

  /// matchedGeometry模式的关闭动画
  private func closeWithMatchedGeometryAnimation() {
    withAnimation(.easeOut(duration: 0.2)) {
      showDetailContent = false
    }

    DispatchQueue.main.asyncAfter(deadline: .now()) {
      withAnimation(.easeInOut(duration: 0.3)) {
        showingCardDetail = false
      }
    }
  }

  /// createCard模式的关闭动画
  private func closeWithCreateCardAnimation() {
    // 第一阶段：创建表单向底部滑出
    withAnimation(.easeOut(duration: 0.2)) {
      showCreateForm = false
    }

    // 第二阶段：卡片滑向上方
    DispatchQueue.main.asyncAfter(deadline: .now() + 0.15) {
      withAnimation(.easeOut(duration: 0.3)) {
        showCard = false
      }
    }

    // 第三阶段：背景渐出同时整体视图关闭
    DispatchQueue.main.asyncAfter(deadline: .now() + 0.35) {
      withAnimation(.easeOut(duration: 0.15)) {
        showBackground = false
        showingCardDetail = false
      }
    }
  }

  private var cardView: some View {
    Group {
      if animationMode == .matchedGeometry, let namespace = cardNamespace, let card = currentCard,
        !isCreatingCard
      {
        // matchedGeometry模式：仅适用于编辑现有卡片的场景
        if viewModel.isEditingCard {
          // 编辑模式显示预览
          Card(viewModel: CardVM.fromCard(previewCard, isEditMode: true))
            .matchedGeometryEffect(id: card.id, in: namespace)
        } else {
          // 普通模式显示原卡片，但保持可翻转功能
          FlippableCardView(
            card: card,
            isFlipped: $viewModel.isFlipped,
            dragAmount: $viewModel.dragAmount,
            rotationAngle: $viewModel.rotationAngle,
            isEditingCard: $viewModel.isEditingCard,
            onShowActionSheet: isCreatingCard
              ? nil
              : {
                viewModel.showCardActionSheet = true
              },
            isCreatingCard: isCreatingCard
          )
          .matchedGeometryEffect(id: card.id, in: namespace)
        }
      } else {
        // slideFromTop模式、createCard模式，或没有namespace的情况
        if animationMode == .createCard && isCreatingCard {
          // 创建模式：始终显示预览资产
          Card(viewModel: CardVM.fromCard(previewCard, isEditMode: true))
            .offset(y: showCard ? 0 : -UIScreen.main.bounds.height)
        } else if let displayCard = (viewModel.isEditingCard ? previewCard : currentCard) {
          // 其他模式：根据编辑状态选择显示的卡片
          FlippableCardView(
            card: displayCard,
            isFlipped: $viewModel.isFlipped,
            dragAmount: $viewModel.dragAmount,
            rotationAngle: $viewModel.rotationAngle,
            isEditingCard: $viewModel.isEditingCard,
            onShowActionSheet: isCreatingCard
              ? nil
              : {
                viewModel.showCardActionSheet = true
              },
            isCreatingCard: isCreatingCard
          )
          .offset(y: showCard ? 0 : -UIScreen.main.bounds.height)
        } else {
          // 创建模式下的占位卡片（不应该再使用）
          RoundedRectangle(cornerRadius: 24)
            .fill(Color.cWhite)
            .frame(height: 190)
            .padding(.horizontal, 16)
            .offset(y: showCard ? 0 : -UIScreen.main.bounds.height)
        }
      }
    }
    .motionEffect(intensity: 0.8)
  }

  private var detailInfoView: some View {
    ZStack {
      ScrollView {
        LazyVStack(spacing: 0) {
          timeSelectionSection
          // 使用VM预先准备好的收入支出数据
          IncomeExpenseCard(viewModel: viewModel.incomeExpenseCardVM)
            .padding(.horizontal, 16)
            .padding(.top, 12)

          transactionSection

          // 为底部按钮留出空间
          Spacer(minLength: 80)
        }
        .padding(.top, 16)
      }
      .offset(x: viewModel.isEditingCard ? -UIScreen.main.bounds.width : 0)
      .opacity(viewModel.isEditingCard ? 0 : 1)

      // 底部关闭按钮
      if !viewModel.isEditingCard {
        detailViewBottomButtons
      }
    }
  }

  private var editingView: some View {
    ZStack {
      ScrollView {
        VStack(spacing: 16) {
          // 编辑头部
          HStack {
            Text(isCreatingCard ? "创建卡片" : "编辑卡片")
              .font(.system(size: 16, weight: .medium))
              .foregroundColor(.cBlack)
            Spacer()

            if !isCreatingCard {
              Button("完成") {
                hapticManager.trigger(.selection)
                viewModel.isEditingCard = false
              }
              .font(.system(size: 14, weight: .medium))
              .foregroundColor(.blue)
            }
          }
          .padding(.horizontal, 16)
          .padding(.top, 8)

          editingFormView
          Spacer(minLength: 80)  // 为底部按钮留出空间
        }
      }
      .offset(x: viewModel.isEditingCard ? 0 : UIScreen.main.bounds.width)
      .opacity(viewModel.isEditingCard ? 1 : 0)

      // 底部编辑按钮
      if viewModel.isEditingCard {
        editingBottomButtons
      }
    }
  }

  private var editingBottomButtons: some View {
    FloatingActionButtonView(
      buttons: [
        FloatingActionButton(
          title: "取消",
          action: {
            hapticManager.trigger(.impactLight)
            cancelCardEditing()
          },
          style: .secondary
        ),
        FloatingActionButton(
          title: "保存",
          action: {
            hapticManager.trigger(.impactMedium)
            handleSaveCard()
          },
          style: .primary
        ),
      ]
    )
  }

  private var editingFormView: some View {
    VStack(spacing: 16) {
      cardNameEditField

      cardAmountEditSection
      if isCreditCard {
        creditCardSettingsSection
      }
      HStack {
        HStack(spacing: 12) {
          Text("卡片背景")
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(.cBlack)

          Text(
            CardCoverHelper.shared.getCoverDisplayName(
              for: viewModel.editSelectedcoverType)
          )
          .font(.system(size: 12, weight: .regular))
          .foregroundColor(.cBlack.opacity(0.6))
        }
        Spacer()

        Image(systemName: "photo.on.rectangle.angled")
          .font(.system(size: 15, weight: .medium))
          .foregroundColor(.cBlack.opacity(0.4))
      }
      .padding(.horizontal, 12)
      .frame(height: 56)
      .background(Color.cWhite)
      .cornerRadius(16)
      .overlay(
        RoundedRectangle(cornerRadius: 16)
          .strokeBorder(Color.cAccentBlue.opacity(0.08), lineWidth: 1)
      )
      .padding(.horizontal, 16)
      .onTapGesture {
        hapticManager.trigger(.selection)
        viewModel.showingCoverOptions = true
        print("打开背景选择器，当前背景: \(viewModel.editSelectedcoverType.rawValue)")
      }
      settingsTogglesView
    }
  }

  private var cardNameEditField: some View {
    VStack(spacing: 8) {
      TextField("请输入卡片名称", text: $viewModel.editCardName)
        .focused($isCardNameFocused)
        .font(.system(size: 15, weight: .regular))
        .foregroundColor(.cBlack)
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(.horizontal, 16)
        .frame(height: 56)
        .background(Color.cWhite)
        .cornerRadius(16)
        .overlay(
          RoundedRectangle(cornerRadius: 16)
            .strokeBorder(Color.cAccentBlue.opacity(isCardNameFocused ? 1 : 0.08), lineWidth: 1)
        )
        .padding(.horizontal, 16)
    }
  }

  private var settingsTogglesView: some View {
    HStack(spacing: 12) {
      // 计入总资产开关 - 内联
      HStack {
        Text("计入总资产")
          .font(.system(size: 14, weight: .medium))
          .foregroundColor(.cBlack)
        Spacer()

        Button(action: {
          withAnimation(.spring(response: 0.2, dampingFraction: 0.7)) {
            viewModel.editIsIncludeInTotal.toggle()
          }
        }) {
          ZStack {
            Capsule()
              .fill(
                viewModel.editIsIncludeInTotal ? Color.cAccentBlue : Color.cAccentBlue.opacity(0.1)
              )
              .frame(width: 44, height: 24)

            Circle()
              .fill(Color.cWhite)
              .frame(width: 20, height: 20)
              .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
              .offset(x: viewModel.editIsIncludeInTotal ? 10 : -10)
          }
        }
      }
      .padding(.horizontal, 12)
      .frame(maxWidth: .infinity)
      .frame(height: 56)
      .background(Color.cWhite)
      .cornerRadius(16)
      .overlay(
        RoundedRectangle(cornerRadius: 16)
          .strokeBorder(Color.cAccentBlue.opacity(0.08), lineWidth: 1)
      )

      // 记账时可选开关 - 内联
      HStack {
        Text("记账时可选")
          .font(.system(size: 14, weight: .medium))
          .foregroundColor(.cBlack)
        Spacer()

        Button(action: {
          withAnimation(.spring(response: 0.2, dampingFraction: 0.7)) {
            viewModel.editIsAvailableForBookkeeping.toggle()
          }
        }) {
          ZStack {
            Capsule()
              .fill(
                viewModel.editIsAvailableForBookkeeping
                  ? Color.cAccentBlue : Color.cAccentBlue.opacity(0.1)
              )
              .frame(width: 44, height: 24)

            Circle()
              .fill(Color.cWhite)
              .frame(width: 20, height: 20)
              .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
              .offset(x: viewModel.editIsAvailableForBookkeeping ? 10 : -10)
          }
        }
      }
      .padding(.horizontal, 12)
      .frame(maxWidth: .infinity)
      .frame(height: 56)
      .background(Color.cWhite)
      .cornerRadius(16)
      .overlay(
        RoundedRectangle(cornerRadius: 16)
          .strokeBorder(Color.cAccentBlue.opacity(0.08), lineWidth: 1)
      )
    }
    .padding(.horizontal, 16)
  }

  /// 取消卡片编辑
  private func cancelCardEditing() {
    viewModel.cancelCardEditing(isCreatingCard: isCreatingCard, card: currentCard)
    if isCreatingCard {
      closeDetailView()
    }
  }

  /// 统一处理卡片保存（创建或更新）
  private func handleSaveCard() {
    do {
      // 判断是否为信用卡
      let cardIsCredit = isCredit == true

      // 根据选择的类别获取logo数据
      let logoData: Data? = {
        if let subCategory = subCategory {
          return UIImage(named: subCategory.displayName)?.pngData()
        } else if let mainCategory = mainCategory {
          return UIImage(named: mainCategory.imageUrl)?.pngData()
        }
        return nil
      }()

      let trimmedName = viewModel.editCardName.trimmingCharacters(in: .whitespacesAndNewlines)
      let balance = viewModel.editBalance  // 信用卡和储蓄卡都直接使用余额

      if isCreatingCard {
        // 获取当前最大的 order 值，新卡片的 order 应该是最大值 + 1
        let maxOrder =
          (try? modelContext.fetch(FetchDescriptor<CardModel>()))?.map(\.order).max() ?? -1
        let newOrder = maxOrder + 1

        // 创建新卡片
        let newCard = CardModel(
          id: UUID(),
          order: newOrder,
          isCredit: cardIsCredit,
          isSelected: viewModel.editIsAvailableForBookkeeping,
          name: trimmedName,
          remark: "",
          currency: viewModel.editSelectedCurrencyCode,
          symbol: viewModel.editCurrencySymbol,
          balance: balance,
          credit: cardIsCredit ? viewModel.editLimit : 0,
          isStatistics: viewModel.editIsIncludeInTotal,
          cover: viewModel.editSelectedcoverType.rawValue,
          bankLogo: logoData,
          bankName: subCategory?.displayName ?? mainCategory?.name ?? "",
          cardNumber: "",
          billDay: cardIsCredit && viewModel.editHasBillDaySet
            ? (viewModel.editBillingDay == 31 ? 0 : viewModel.editBillingDay) : nil,
          isFixedDueDay: viewModel.editRepaymentType == .fixedDay,
          dueDay: cardIsCredit && viewModel.editHasDueDaySet ? viewModel.editRepaymentDay : nil,
          createdAt: Date(),
          updatedAt: Date()
        )

        // 保存新卡片到数据库
        modelContext.insert(newCard)
        try modelContext.save()

        print("✅ 卡片创建成功: \(newCard.name)")

        // 创建卡片创建交易（包含余额重新计算）
        createCardCreationTransaction(for: newCard)

        // 创建成功后关闭视图
        closeDetailView()
      } else {
        // 更新现有卡片
        guard let existingCard = currentCard else { return }

        // 记录原始余额，用于判断是否需要创建调整交易
        let originalBalance = existingCard.balance

        // 更新卡片属性
        existingCard.name = trimmedName
        existingCard.isSelected = viewModel.editIsAvailableForBookkeeping
        existingCard.isStatistics = viewModel.editIsIncludeInTotal
        existingCard.balance = balance
        existingCard.currency = viewModel.editSelectedCurrencyCode
        existingCard.symbol = viewModel.editCurrencySymbol
        existingCard.cover = viewModel.editSelectedcoverType.rawValue

        // 信用卡特有属性
        if existingCard.isCredit {
          existingCard.credit = viewModel.editLimit
          existingCard.billDay =
            viewModel.editHasBillDaySet
            ? (viewModel.editBillingDay == 31 ? 0 : viewModel.editBillingDay) : nil
          existingCard.isFixedDueDay = viewModel.editRepaymentType == .fixedDay
          existingCard.dueDay = viewModel.editHasDueDaySet ? viewModel.editRepaymentDay : nil
        }

        existingCard.updatedAt = Date()

        // 保存到数据库
        try modelContext.save()

        print("✅ 卡片更新成功: \(existingCard.name)")

        // 只有在余额发生变化时才创建调整交易
        if abs(originalBalance - balance) > 0.001 {  // 使用小的阈值来处理浮点数精度问题
          print("💰 检测到余额变化: \(originalBalance) -> \(balance)，创建调整交易")
          createCardAdjustmentTransaction(for: existingCard)
        } else {
          print("📝 余额未变化，跳过创建调整交易")
        }

        // 退出编辑模式
        viewModel.isEditingCard = false
      }
    } catch {
      print("❌ 卡片保存失败: \(error.localizedDescription)")
    }
  }

  /// 创建卡片创建交易记录
  private func createCardCreationTransaction(for card: CardModel) {
    do {
      let creationTransaction = createSystemTransaction(
        type: .createCard,
        card: card,
        amount: card.balance,
        remark: "创建卡片"
      )

      modelContext.insert(creationTransaction)
      try modelContext.save()
      print("✅ 卡片创建交易记录创建成功，金额: \(card.balance)")

      // 使用统一的服务方法重新计算余额
      BalanceRecalculationService.shared.recalculateBalances(
        for: [creationTransaction.fromCardId, creationTransaction.toCardId],
        modelContext: modelContext,
        currencies: legacyCurrencies,
        operation: "卡片创建"
      )
    } catch {
      print("❌ 卡片创建交易记录创建失败: \(error.localizedDescription)")
    }
  }

  /// 创建卡片调整交易记录
  private func createCardAdjustmentTransaction(for card: CardModel) {
    do {
      let adjustmentTransaction = createSystemTransaction(
        type: .adjustCard,
        card: card,
        amount: card.balance,  // 调整交易金额为当前卡片余额
        remark: "调整卡片信息"
      )

      modelContext.insert(adjustmentTransaction)
      try modelContext.save()
      print("✅ 卡片调整交易记录创建成功")

      // 使用统一的服务方法重新计算余额
      BalanceRecalculationService.shared.recalculateBalances(
        for: [adjustmentTransaction.fromCardId, adjustmentTransaction.toCardId],
        modelContext: modelContext,
        currencies: legacyCurrencies,
        operation: "卡片调整"
      )
    } catch {
      print("❌ 卡片调整交易记录创建失败: \(error.localizedDescription)")
    }
  }

  /// 创建系统交易的通用方法
  private func createSystemTransaction(
    type: TransactionType,
    card: CardModel,
    amount: Double,
    remark: String
  ) -> TransactionModel {
    let systemCategoryId: String
    switch type {
    case .createCard:
      systemCategoryId = "SYS_CREATE_CARD"
    case .adjustCard:
      systemCategoryId = "SYS_ADJUST_CARD"
    default:
      systemCategoryId = "SYS_OTHER"
    }

    // 对于创建卡片和调整卡片交易，保持原始金额（包括负数）
    // 这样重新计算余额时能正确处理信用卡的负余额
    let transactionAmount: Double
    if type == .createCard || type == .adjustCard {
      transactionAmount = amount  // 保持原始金额，包括负数
    } else {
      transactionAmount = abs(amount)  // 其他交易类型使用绝对值
    }

    return TransactionModel(
      id: UUID(),
      originalTransactionId: nil,
      transactionType: type,
      transactionCategoryId: systemCategoryId,
      fromCardId: nil,
      toCardId: card.id,
      discountAmount: nil,
      transactionAmount: transactionAmount,
      refundAmount: nil,
      currency: card.currency,
      symbol: card.symbol,
      expenseToCardRate: 1.0,
      expenseToBaseRate: 1.0,
      incomeToCardRate: 1.0,
      incomeToBaseRate: 1.0,
      isStatistics: false,
      remark: remark,
      originalTradId: nil,
      transactionDate: Date(),
      createdAt: Date(),
      updatedAt: Date()
    )
  }

  /// 创建余额调整交易记录
  private func createBalanceAdjustmentTransaction(for card: CardModel, newBalance: Double) {
    do {
      let balanceDifference = newBalance - card.balance

      let adjustmentTransaction = TransactionModel(
        id: UUID(),
        originalTransactionId: nil,
        transactionType: .adjustCard,
        transactionCategoryId: nil,
        fromCardId: nil,
        toCardId: card.id,
        discountAmount: nil,
        transactionAmount: abs(balanceDifference),
        refundAmount: nil,
        currency: card.currency,
        symbol: card.symbol,
        expenseToCardRate: 1.0,
        expenseToBaseRate: 1.0,
        incomeToCardRate: 1.0,
        incomeToBaseRate: 1.0,
        isStatistics: false,
        remark: "调整卡片余额",
        originalTradId: nil,
        transactionDate: Date(),
        createdAt: Date(),
        updatedAt: Date()
      )

      modelContext.insert(adjustmentTransaction)
      try modelContext.save()
      print("✅ 余额调整交易记录创建成功，调整金额: \(balanceDifference)")
    } catch {
      print("❌ 余额调整交易记录创建失败: \(error.localizedDescription)")
    }
  }

  /// 格式化日期字符串
  private func formatDateString(for period: TransactionTimePeriod, date: Date) -> String {
    switch period {
    case .week:
      return DateFormattingHelper.shared.format(date: date, with: "yyyy年MM月第W周")
    case .month:
      return DateFormattingHelper.shared.format(date: date, with: "yyyy年MM月")
    case .year:
      return DateFormattingHelper.shared.format(date: date, with: "yyyy年")
    }
  }

  // MARK: - 子视图组件

  /// 时间选择区域
  private var timeSelectionSection: some View {
    HStack {
      Text("统计")
        .font(.system(size: 15, weight: .medium))
        .foregroundColor(.cBlack)
      Spacer()

      TimeControl(viewModel: viewModel.timeControlVM, style: .inline)
    }
    .padding(.horizontal, 16)
  }

  /// 交易记录区域
  private var transactionSection: some View {
    VStack(spacing: 0) {
      HStack {
        Text("交易记录")
          .font(.system(size: 15, weight: .medium))
          .foregroundColor(.cBlack)
        Spacer()
      }
      .padding(.horizontal, 16)
      .padding(.top, 24)
      .padding(.bottom, 12)

      // 使用统一的TransactionListContent组件
      if let groupedTransactions = viewModel.cardDetailData?.groupedTrandes,
        let card = currentCard
      {
        let transactionDayGroups = viewModel.convertToTransactionDayGroups(
          groupedTransactions: groupedTransactions,
          card: card,
          pathManager: pathManager
        )

        TransactionListContent(
          transactionDayGroups: transactionDayGroups,
          currencySymbol: card.symbol,
          hasTransactions: !groupedTransactions.isEmpty,
          emptyStateConfig: TransactionListContentVM.EmptyStateConfig(
            icon: "404",
            text: "暂无交易记录",
            useSystemIcon: false
          )
        )
      } else {
        // 空状态视图
        VStack(spacing: 12) {
          Image("404")
            .resizable()
            .frame(width: 80, height: 80)

          Text("暂无交易记录")
            .font(.system(size: 14, weight: .regular))
            .foregroundColor(.cBlack.opacity(0.6))
        }
        .frame(height: 120)
        .frame(maxWidth: .infinity)
        .padding(.horizontal, 16)
      }
    }
  }

  // MARK: - 编辑界面组件

  private var cardAmountEditSection: some View {
    VStack(spacing: 12) {
      // 储蓄卡
      if !isCreditCard {
        AmountDisplayCard(
          title: "卡片余额",
          amount: viewModel.editBalance,
          isSelected: viewModel.editingAmount == .balance && viewModel.showingNumericKeypad,
          currencyCode: viewModel.getCurrentCurrencyCode(for: currentCard),
          currencySymbol: viewModel.getCurrentCurrencySymbol(for: currentCard),
          onCurrencyTap: {
            viewModel.showingCurrencyPicker = true
          }
        )
        .onTapGesture {
          viewModel.showAmountEditKeypad(for: .balance, amount: viewModel.editBalance)
        }
      }

      // 信用卡显示余额和额度
      if isCreditCard {
        AmountDisplayCard(
          title: "卡片余额",
          amount: viewModel.editBalance,
          textColor: viewModel.editBalance >= 0 ? .primary : .red,
          isSelected: viewModel.editingAmount == .balance && viewModel.showingNumericKeypad,
          currencyCode: viewModel.getCurrentCurrencyCode(for: currentCard),
          currencySymbol: viewModel.getCurrentCurrencySymbol(for: currentCard),
          onCurrencyTap: {
            viewModel.showingCurrencyPicker = true
          }
        )
        .onTapGesture {
          viewModel.showAmountEditKeypad(for: .balance, amount: viewModel.editBalance)
        }

        AmountDisplayCard(
          title: "卡片额度",
          amount: viewModel.editLimit,
          isSelected: viewModel.editingAmount == .limit && viewModel.showingNumericKeypad,
          currencyCode: viewModel.getCurrentCurrencyCode(for: currentCard),
          currencySymbol: viewModel.getCurrentCurrencySymbol(for: currentCard),
          onCurrencyTap: {
            viewModel.showingCurrencyPicker = true
          }
        )
        .onTapGesture {
          viewModel.showAmountEditKeypad(for: .limit, amount: viewModel.editLimit)
        }
      }
    }
  }

  private var creditCardSettingsSection: some View {
    VStack(spacing: 12) {
      // 账单日设置
      HStack {
        HStack(spacing: 12) {
          Text("账单日")
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(.cBlack)
          Text(viewModel.billingDayDisplayText)
            .font(.system(size: 12, weight: .regular))
            .foregroundColor(.cBlack.opacity(0.6))
        }
        Spacer()
        Button(action: {
          viewModel.showBillingDayPicker()
        }) {
          Image(systemName: "calendar")
            .font(.system(size: 15, weight: .medium))
            .foregroundColor(
              viewModel.editHasBillDaySet ? Color.cAccentBlue : Color.cBlack.opacity(0.4))
        }
      }
      .padding(.horizontal, 12)
      .frame(height: 56)
      .background(Color.cWhite)
      .cornerRadius(16)
      .overlay(
        RoundedRectangle(cornerRadius: 16)
          .strokeBorder(
            Color.cAccentBlue.opacity(viewModel.showingBillingDayPicker ? 1 : 0.08),
            lineWidth: 1)
      )
      .padding(.horizontal, 16)

      // 还款日设置
      HStack {
        HStack(spacing: 12) {
          Text("还款日")
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(.cBlack)
          Text(viewModel.repaymentDayDisplayText)
            .font(.system(size: 12, weight: .regular))
            .foregroundColor(.cBlack.opacity(0.6))
        }
        Spacer()
        Button(action: {
          viewModel.showRepaymentDayPicker()
        }) {
          Image(systemName: "calendar")
            .font(.system(size: 15, weight: .medium))
            .foregroundColor(
              viewModel.editHasDueDaySet ? Color.cAccentBlue : Color.cBlack.opacity(0.4))
        }
      }
      .padding(.horizontal, 12)
      .frame(height: 56)
      .background(Color.cWhite)
      .cornerRadius(16)
      .overlay(
        RoundedRectangle(cornerRadius: 16)
          .strokeBorder(
            Color.cAccentBlue.opacity(viewModel.showingRepaymentDayPicker ? 1 : 0.08),
            lineWidth: 1)
      )
      .padding(.horizontal, 16)
    }
  }

  // 背景选择器已移除

}

// MARK: - 便利初始化器

extension CardDetailOverlayView {
  /// 用于HomeView的便利初始化器（从顶部滑入）
  init(
    card: CardModel?,
    pathManager: PathManagerHelper,
    transactions: [TransactionModel],
    currencies: [CurrencyModel],
    showingCardDetail: Binding<Bool>,
    isCreatingCard: Bool,
    isCredit: Bool?,
    mainCategory: CardCategoryResponse?,
    subCategory: CardSubCategoryResponse?,
    onCardCreated: ((CardModel) -> Void)?
  ) {
    // 使用兼容性初始化方法
    self.init(
      card: card,
      cardNamespace: nil,
      showingCardDetail: showingCardDetail,
      pathManager: pathManager,
      transactions: transactions,
      currencies: currencies,
      animationMode: .slideFromTop,
      isCreatingCard: isCreatingCard,
      isCredit: isCredit,
      mainCategory: mainCategory,
      subCategory: subCategory,
      onCardCreated: onCardCreated
    )

  }

  /// 用于CardView的便利初始化器（matchedGeometry动画）
  init(
    card: CardModel,
    cardNamespace: Namespace.ID,
    pathManager: PathManagerHelper,
    transactions: [TransactionModel],
    currencies: [CurrencyModel],
    showingCardDetail: Binding<Bool>,
    isCreatingCard: Bool = false,
    isCredit: Bool? = nil,
    mainCategory: CardCategoryResponse? = nil,
    subCategory: CardSubCategoryResponse? = nil,
    onCardCreated: ((CardModel) -> Void)? = nil
  ) {
    // 使用兼容性初始化方法
    self.init(
      card: card,
      cardNamespace: cardNamespace,
      showingCardDetail: showingCardDetail,
      pathManager: pathManager,
      transactions: transactions,
      currencies: currencies,
      animationMode: .matchedGeometry,
      isCreatingCard: isCreatingCard,
      isCredit: isCredit,
      mainCategory: mainCategory,
      subCategory: subCategory,
      onCardCreated: onCardCreated
    )
  }

  /// 用于创建卡片的便利初始化器（专门的创建卡片动画）
  init(
    pathManager: PathManagerHelper,
    showingCardDetail: Binding<Bool>,
    isCredit: Bool,
    mainCategory: CardCategoryResponse?,
    subCategory: CardSubCategoryResponse?,
    onCardCreated: ((CardModel) -> Void)?
  ) {
    // 使用新架构初始化方法
    self.init(
      cardId: nil,
      cardNamespace: nil,
      showingCardDetail: showingCardDetail,
      pathManager: pathManager,
      animationMode: .createCard,
      isCreatingCard: true,
      isCredit: isCredit,
      mainCategory: mainCategory,
      subCategory: subCategory,
      onCardCreated: onCardCreated
    )
  }
}

// MARK: - 3D翻转卡片组件

/// 可翻转的卡片视图，支持3D翻转效果
private struct FlippableCardView: View {
  let card: CardModel?
  @Binding var isFlipped: Bool
  @Binding var dragAmount: CGFloat
  @Binding var rotationAngle: Double
  @Binding var isEditingCard: Bool
  var onShowActionSheet: (() -> Void)? = nil
  var isCreatingCard: Bool = false

  // 调试信息
  private var debugInfo: String {
    "卡片ID: \(card?.id.uuidString.prefix(8) ?? "nil"), 背景: \(card?.cover ?? "nil")"
  }

  var body: some View {
    ZStack {
      // 卡片正面
      if let card = card {
        Card(viewModel: CardVM.fromCard(card))
          .opacity(isFlipped ? 0 : 1)
          .rotation3DEffect(
            Angle.degrees(rotationAngle),
            axis: (x: 0, y: 1, z: 0)
          )
      } else {
        // 创建模式下显示空白卡片或占位符
        RoundedRectangle(cornerRadius: 24)
          .fill(Color.cWhite)
          .frame(height: 190)
          .padding(.horizontal, 16)
          .opacity(isFlipped ? 0 : 1)
          .rotation3DEffect(
            Angle.degrees(rotationAngle),
            axis: (x: 0, y: 1, z: 0)
          )
      }

      // 卡片背面
      CardBackView(
        card: card,
        isEditingCard: $isEditingCard,
        isFlipped: $isFlipped,
        rotationAngle: $rotationAngle,
        onShowActionSheet: onShowActionSheet,
        isCreatingCard: isCreatingCard
      )
      .opacity(isFlipped ? 1 : 0)
      .rotation3DEffect(
        Angle.degrees(rotationAngle + 180),
        axis: (x: 0, y: 1, z: 0)
      )
    }
    .onTapGesture {
      // 编辑模式下禁用卡片翻转
      guard !isEditingCard else { return }

      // 点击卡片翻转，阻止事件传播
      withAnimation(.easeInOut(duration: 0.6)) {
        isFlipped.toggle()
        rotationAngle = isFlipped ? 180 : 0
      }
    }
    .onAppear {
      // 确保初始状态
      rotationAngle = isFlipped ? 180 : 0
    }
  }
}

/// 卡片背面视图
private struct CardBackView: View {
  let card: CardModel?
  @Binding var isEditingCard: Bool
  @Binding var isFlipped: Bool
  @Binding var rotationAngle: Double
  var onShowActionSheet: (() -> Void)? = nil
  var isCreatingCard: Bool = false
  var body: some View {
    ScrollView(showsIndicators: false) {
      LazyVStack(spacing: 12) {
        // 顶部标题
        HStack {
          Text("卡片详情")
            .font(.system(size: 16, weight: .medium))
            .foregroundColor(.cBlack)
          Spacer()
          if !isCreatingCard {
            Button(action: {
              onShowActionSheet?()
            }) {
              Image(systemName: "ellipsis")
                .font(.system(size: 20))
                .foregroundColor(.cBlack.opacity(0.6))
            }
          }
        }
        // 中间信息区域
        VStack(spacing: 16) {
          // 卡片信息
          InfoRow(title: "卡片类型", value: card?.isCredit == true ? "信用卡" : "储蓄卡")
          InfoRow(title: "货币类型", value: card?.symbol ?? "¥")

          if card?.isCredit == true {
            InfoRow(
              title: "信用额度",
              value:
                "\(card?.symbol ?? "¥")\(NumberFormatService.shared.formatAmount(card?.credit ?? 0))"
            )
            if let billDay = card?.billDay {
              InfoRow(title: "账单日", value: billDay == 0 ? "月末" : "\(billDay)号")
            }
            if let dueDay = card?.dueDay {
              InfoRow(title: "还款日", value: "\(dueDay)号")
            }
          }
        }
        Spacer()
      }
      .padding(16)
    }

    .cornerRadius(24)

    .frame(height: 190)

    .background(
      Color.cWhite
        .cornerRadius(24)

    )
    .padding(.horizontal, 16)

  }

  /// 信息行组件
  private struct InfoRow: View {
    let title: String
    let value: String

    var body: some View {
      HStack {
        Text(title)
          .font(.system(size: 14))
          .foregroundColor(.cBlack.opacity(0.6))
        Spacer()
        Text(value)
          .font(.system(size: 14, weight: .medium))
          .foregroundColor(.cBlack)
      }
    }
  }
}

// MARK: - 日期选择器组件

/// 日期选择器视图
private struct DayPickerView: View {
  @Binding var selectedDay: Int
  @Binding var repaymentType: CardDetailOverlayVM.RepaymentType
  var onSave: () -> Void
  var onClear: (() -> Void)? = nil
  var isRepaymentDay: Bool = false

  var body: some View {
    VStack(spacing: 0) {
      // 标题栏
      HStack {
        Text(isRepaymentDay ? "选择还款日" : "选择账单日")
          .font(.system(size: 16, weight: .medium))
          .foregroundColor(.cBlack)
        Spacer()
        if let onClear = onClear {
          Button(action: onClear) {
            Text("不设置")
              .font(.system(size: 14, weight: .medium))
              .frame(height: 24)
              .padding(.horizontal, 8)
              .background(Color.cAccentBlue.opacity(0.1))
              .cornerRadius(12)
              .foregroundColor(.cAccentBlue)
          }
          .padding(.trailing, 8)
        }
        Button(action: onSave) {
          Text("确定")
            .font(.system(size: 14, weight: .medium))
            .frame(height: 24)
            .padding(.horizontal, 8)
            .background(Color.cAccentBlue)
            .cornerRadius(12)
            .foregroundColor(.cWhite)
        }
      }
      .padding(.horizontal, 16)
      .padding(.vertical, 12)

      // 选择器
      if isRepaymentDay {
        HStack(spacing: 0) {
          // 还款类型选择器
          Picker("还款类型", selection: $repaymentType) {
            ForEach(
              [
                CardDetailOverlayVM.RepaymentType.fixedDay,
                CardDetailOverlayVM.RepaymentType.afterBilling,
              ], id: \.self
            ) { type in
              Text(type.rawValue)
                .tag(type)
            }
          }
          .pickerStyle(.wheel)
          .frame(width: (UIScreen.main.bounds.width - 48) * 0.5)

          // 日期选择器
          Picker("日期", selection: $selectedDay) {
            ForEach(1...31, id: \.self) { day in
              if repaymentType == .fixedDay {
                Text(day == 31 ? "月末" : "每月\(day)日")
                  .tag(day)
              } else {
                Text("\(day)日")
                  .tag(day)
              }
            }
          }
          .pickerStyle(.wheel)
          .frame(width: (UIScreen.main.bounds.width - 48) * 0.5)
        }
        .padding(.horizontal, 8)
      } else {
        // 账单日选择器
        Picker("日期", selection: $selectedDay) {
          ForEach(1...31, id: \.self) { day in
            Text(day == 31 ? "月末" : "每月\(day)日")
              .tag(day)
          }
        }
        .pickerStyle(.wheel)
        .padding(.horizontal, 8)
      }

      Spacer()
    }
    .background(Color.cBeige)
  }
}

// MARK: - 通用组件

/// 金额显示卡片组件
private struct AmountDisplayCard: View {
  let title: String
  let amount: Double
  var textColor: Color = Color.cBlack
  var isSelected: Bool = false
  var currencyCode: String
  var currencySymbol: String
  var onCurrencyTap: (() -> Void)?

  var body: some View {
    VStack(alignment: .leading, spacing: 4) {
      Text(title)
        .font(.system(size: 12, weight: .medium))
        .foregroundColor(.cBlack.opacity(0.4))
        .frame(maxWidth: .infinity, alignment: .leading)

      HStack {
        DisplayCurrencyView.size18(
          symbol: currencySymbol,
          amount: amount
        )
        .foregroundColor(textColor)
        .contentTransition(.numericText(value: amount))
        .animation(.easeInOut(duration: 0.3), value: amount)

        Spacer()

        Button(action: {
          onCurrencyTap?()
        }) {
          HStack(spacing: 4) {
            Text(currencyCode)
              .font(.system(size: 14, weight: .regular))
              .foregroundColor(.cBlack.opacity(0.6))
            Image(systemName: "chevron.right")
              .font(.system(size: 12, weight: .medium))
              .foregroundColor(.cBlack.opacity(0.6))
          }
        }
      }
    }
    .padding(.horizontal, 12)
    .frame(height: 80)
    .background(Color.cWhite)
    .cornerRadius(16)
    .overlay(
      RoundedRectangle(cornerRadius: 16)
        .strokeBorder(Color.cAccentBlue.opacity(isSelected ? 1 : 0.08), lineWidth: 1)
    )
    .padding(.horizontal, 16)
  }
}
