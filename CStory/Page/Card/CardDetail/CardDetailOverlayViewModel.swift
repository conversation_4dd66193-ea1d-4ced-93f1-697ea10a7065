//
//  CardDetailOverlayVM.swift
//  CStory
//
//  Created by NZUE on 2025/12/19.
//

import Foundation
import SwiftData
import SwiftUI

// MARK: - Sendable数据结构

/// Sendable版本的CardModel数据
struct CardModelData: Sendable {
  let id: UUID
  let order: Int
  let isCredit: Bool
  let isSelected: Bool
  let name: String
  let remark: String
  let currency: String
  let symbol: String
  let balance: Double
  let credit: Double
  let isStatistics: Bool
  let cover: String
  let bankName: String
  let cardNumber: String
  let billDay: Int?
  let isFixedDueDay: Bool
  let dueDay: Int?
  let createdAt: Date
  let updatedAt: Date

  init(from card: CardModel) {
    self.id = card.id
    self.order = card.order
    self.isCredit = card.isCredit
    self.isSelected = card.isSelected
    self.name = card.name
    self.remark = card.remark
    self.currency = card.currency
    self.symbol = card.symbol
    self.balance = card.balance
    self.credit = card.credit
    self.isStatistics = card.isStatistics
    self.cover = card.cover
    self.bankName = card.bankName
    self.cardNumber = card.cardNumber
    self.billDay = card.billDay
    self.isFixedDueDay = card.isFixedDueDay
    self.dueDay = card.dueDay
    self.createdAt = card.createdAt
    self.updatedAt = card.updatedAt
  }
}

/// Sendable版本的TransactionModel数据
struct TransactionModelData: Sendable {
  let id: UUID
  let transactionType: TransactionType
  let fromCardId: UUID?
  let toCardId: UUID?
  let transactionAmount: Double
  let currency: String
  let symbol: String
  let transactionDate: Date
  /// 支出货币到卡片货币汇率
  let expenseToCardRate: Double
  /// 支出货币到本位币汇率
  let expenseToBaseRate: Double
  /// 收入货币到卡片货币汇率
  let incomeToCardRate: Double
  /// 收入货币到本位币汇率
  let incomeToBaseRate: Double

  init(from transaction: TransactionModel) {
    self.id = transaction.id
    self.transactionType = transaction.transactionType
    self.fromCardId = transaction.fromCardId
    self.toCardId = transaction.toCardId
    self.transactionAmount = transaction.transactionAmount
    self.currency = transaction.currency
    self.symbol = transaction.symbol
    self.transactionDate = transaction.transactionDate
    self.expenseToCardRate = transaction.expenseToCardRate
    self.expenseToBaseRate = transaction.expenseToBaseRate
    self.incomeToCardRate = transaction.incomeToCardRate
    self.incomeToBaseRate = transaction.incomeToBaseRate
  }
}

/// Sendable版本的CurrencyModel数据
struct CurrencyModelData: Sendable {
  let code: String
  let symbol: String
  let rate: Double

  init(from currency: CurrencyModel) {
    self.code = currency.code
    self.symbol = currency.symbol
    self.rate = currency.rate
  }
}

/// 卡片详情覆盖视图模型
///
/// 负责卡片详情覆盖视图的业务逻辑处理和数据管理，遵循新架构的MVVM模式。
/// 该类从DataManagement获取原始数据，进行业务逻辑处理后，
/// 为CardDetailOverlayView提供格式化的、可直接使用的数据。
///
/// ## 主要职责
/// - 卡片详情数据加载和展示
/// - 卡片编辑状态和交互管理
/// - 创建/查看/编辑三种模式的状态管理
/// - 卡片动画和UI状态控制
/// - 交易数据统计和格式化
///
/// ## 模式说明
/// - **创建模式** (`isCreatingCard = true`): 创建新卡片
/// - **查看模式** (`isCreatingCard = false, isEditingCard = false`): 查看卡片详情
/// - **编辑模式** (`isCreatingCard = false, isEditingCard = true`): 编辑现有卡片
///
/// ## 数据流向
/// ```
/// DataManagement → CardDetailOverlayVM → CardDetailOverlayView
/// ```
///
/// - Important: 使用DataManagement替代AppDataManager，确保数据一致性
/// - Note: 所有UI相关的数据处理都在ViewModel层完成
/// - Author: NZUE
/// - Version: 2.0 (New架构)
/// - Since: 2025.7.17
@MainActor
final class CardDetailOverlayVM: ObservableObject {

  // MARK: - Dependencies

  /// 数据管理器，提供原始数据源
  ///
  /// 通过DataManagement获取卡片、交易、分类等基础数据，
  /// 作为所有业务逻辑计算的数据来源。
  private var dataManager: DataManagement?

  // MARK: - Published Properties

  /// 时间控制器 - 管理时间选择逻辑
  @Published var timeControlVM: TimeControlVM

  /// 当前卡片（用于响应式数据更新）
  @Published var currentCard: CardModel?

  // MARK: - Card UI State

  /// 卡片是否翻转
  @Published var isFlipped: Bool = false

  /// 拖拽偏移量
  @Published var dragAmount: CGFloat = 0

  /// 旋转角度
  @Published var rotationAngle: Double = 0

  /// 是否正在编辑卡片
  @Published var isEditingCard: Bool = false

  // MARK: - Card Edit State

  /// 编辑中的卡片名称
  @Published var editCardName: String = ""

  /// 编辑中的余额
  @Published var editBalance: Double = 0.0

  /// 编辑中的债务
  @Published var editDebt: Double = 0.0

  /// 编辑中的额度
  @Published var editLimit: Double = 0.0

  /// 是否包含在总计中
  @Published var editIsIncludeInTotal: Bool = true

  /// 是否可用于记账
  @Published var editIsAvailableForBookkeeping: Bool = true

  /// 选择的货币代码
  @Published var editSelectedCurrencyCode: String = ""

  /// 货币符号
  @Published var editCurrencySymbol: String = ""

  /// 选择的封面类型
  @Published var editSelectedcoverType: CardCoverType = .card1

  /// 是否为深色封面
  @Published var editisCoverDark: Bool = false

  // MARK: - Credit Card State

  /// 账单日
  @Published var editBillingDay: Int? = nil

  /// 还款日
  @Published var editRepaymentDay: Int? = nil

  /// 还款类型
  @Published var editRepaymentType: RepaymentType = .fixedDay

  /// 是否设置了账单日
  @Published var editHasBillDaySet = false

  /// 是否设置了还款日
  @Published var editHasDueDaySet = false

  // MARK: - UI Control State

  /// 显示数字键盘
  @Published var showingNumericKeypad = false

  /// 显示账单日选择器
  @Published var showingBillingDayPicker = false

  /// 显示还款日选择器
  @Published var showingRepaymentDayPicker = false

  /// 显示封面选项
  @Published var showingCoverOptions = false

  /// 显示货币选择器
  @Published var showingCurrencyPicker = false

  /// 选择的日期
  @Published var selectedDay: Int = 1

  /// 临时金额
  @Published var tempAmount: String = "0"

  /// 编辑金额类型
  @Published var editingAmount: EditingAmount?

  /// 聚焦的日期字段
  @Published var focusedDateField: DateField?

  /// 显示警告
  @Published var showingAlert = false

  /// 警告信息
  @Published var alertMessage = ""

  /// 显示卡片操作表单
  @Published var showCardActionSheet = false

  // MARK: - Card Detail Data State

  /// 是否正在加载卡片详情
  @Published var isLoadingCardDetail = false

  /// 卡片详情错误信息
  @Published var cardDetailErrorMessage: String?

  /// 卡片详情数据
  @Published var cardDetailData: CardDetailData?

  /// 收入支出卡片的ViewModel
  ///
  /// 预先准备好的收入支出统计数据，包含收入、支出金额和货币符号。
  /// View层可以直接使用，无需在UI层组装数据。
  @Published var incomeExpenseCardVM: IncomeExpenseCardVM

  // MARK: - Private Properties

  /// 原始交易数据（用于主线程处理）
  private var originalTransactions: [TransactionModel] = []

  /// 原始货币数据（用于主线程处理）
  private var originalCurrencies: [CurrencyModel] = []

  // MARK: - Computed Properties

  /// 基础货币代码
  var baseCurrencyCode: String {
    CurrencyService.shared.baseCurrencyCode
  }

  /// 基础货币符号
  var baseCurrencySymbol: String {
    dataManager?.currencies.first { $0.code == baseCurrencyCode }?.symbol ?? "¥"
  }

  /// 所有卡片
  var allCards: [CardModel] {
    dataManager?.cards ?? []
  }

  /// 所有交易
  var allTransactions: [TransactionModel] {
    dataManager?.recentTransactions ?? []
  }

  /// 所有货币
  var allCurrencies: [CurrencyModel] {
    dataManager?.currencies ?? []
  }

  // MARK: - UI Data Properties

  /// 基础货币数组（用于各种计算）
  var baseCurrencies: [CurrencyModel] {
    dataManager?.currencies.filter { $0.isBaseCurrency } ?? []
  }

  /// 默认货币信息
  func getDefaultCurrency() -> (code: String, symbol: String) {
    defaultCurrency(baseCurrency: baseCurrencies)
  }

  /// 获取当前货币代码
  func getCurrentCurrencyCode(for card: CardModel?) -> String {
    currentCurrencyCode(card: card, baseCurrency: baseCurrencies)
  }

  /// 获取当前货币符号
  func getCurrentCurrencySymbol(for card: CardModel?) -> String {
    currentCurrencySymbol(card: card, baseCurrency: baseCurrencies)
  }

  /// 创建预览卡片
  func getPreviewCard(
    card: CardModel?,
    isCreatingCard: Bool,
    isCredit: Bool?,
    subCategory: CardSubCategoryResponse?,
    mainCategory: CardCategoryResponse?
  ) -> CardModel {
    createPreviewCard(
      card: card,
      isCreatingCard: isCreatingCard,
      isCredit: isCredit,
      subCategory: subCategory,
      mainCategory: mainCategory,
      baseCurrency: baseCurrencies
    )
  }

  /// 获取货币符号
  func getCurrencySymbol(for currencyCode: String) -> String {
    dataManager?.currencies.first(where: { $0.code == currencyCode })?.symbol ?? "¥"
  }

  /// 获取交易分类信息
  func getCategoryInfo(for transaction: TransactionModel) -> (name: String, icon: IconType?) {
    guard let categoryId = transaction.transactionCategoryId else {
      return (name: "未知分类", icon: .emoji("❓"))
    }

    guard let dataManager = dataManager else {
      return (name: "未知分类", icon: .emoji("❓"))
    }

    let categoryInfo = dataManager.getCategoryInfo(for: categoryId)
    return (name: categoryInfo.name, icon: categoryInfo.icon)
  }

  /// 将旧格式的交易数据转换为新架构的TransactionDayGroupWithRowVM格式
  func convertToTransactionDayGroups(
    groupedTransactions: [(
      date: Date, transactions: [TransactionModel], income: Double, expense: Double
    )],
    card: CardModel,
    pathManager: PathManagerHelper
  ) -> [TransactionDayGroupWithRowVM] {
    return groupedTransactions.map { group in
      // 转换交易为TransactionRowVM
      let transactionRowVMs = group.transactions.map { transaction in
        // 获取分类信息
        let categoryInfo = getCategoryInfo(for: transaction)

        // 计算余额（如果需要显示）
        let balanceAfterTransaction = BalanceRecalculationService.shared
          .calculateBalanceAtTimePoint(
            card: card,
            targetDate: transaction.transactionDate,
            transactions: allTransactions,
            currencies: allCurrencies
          )

        // 创建TransactionRowVM
        let rowVM = TransactionRowVM(
          transaction: transaction,
          relatedCard: card,  // 传入当前卡片以便判断转账角色
          categoryInfo: categoryInfo,
          displayContext: .cardDetail,  // 卡片详情页上下文
          onTap: {
            pathManager.path.append(NavigationDestination.transactionDetailView(transaction.id))
          }
        )

        // 设置余额文本（卡片详情页显示余额）
        rowVM.balanceText =
          "余额 \(card.symbol)\(NumberFormatService.shared.formatAmount(balanceAfterTransaction))"

        return rowVM
      }

      // 格式化日期文本
      let dateText = DateFormattingHelper.shared.formatDateHeader(date: group.date)

      return TransactionDayGroupWithRowVM(
        dateText: dateText,
        dayIncome: group.income,
        dayExpense: group.expense,
        transactionRowVMs: transactionRowVMs
      )
    }
  }

  // MARK: - Initialization

  /// 初始化卡片详情覆盖视图模型
  ///
  /// 创建CardDetailOverlayVM实例。数据管理器将通过configure方法注入。
  ///
  /// - Note: 使用依赖注入模式，支持不同的初始化场景
  init() {
    // 默认初始化，dataManager 将通过 configure 方法注入
    // 初始化收入支出卡片ViewModel
    self.incomeExpenseCardVM = IncomeExpenseCardVM(
      income: 0.0,
      expense: 0.0,
      currencySymbol: "¥"
    )

    // 初始化时间控制器 - 先不设置回调
    self.timeControlVM = TimeControlVM(selectedPeriod: .month, currentDate: Date()) { _, _ in }

    // 设置时间控制器的回调
    self.timeControlVM.onDateChange = { [weak self] date, period in
      if let card = self?.currentCard {
        self?.loadCardDetailData(card: card, selectedPeriod: period, currentDate: date)
      }
    }
  }

  // MARK: - Computed Properties

  /// 当前选择的时间周期（从时间控制器获取）
  var selectedPeriod: TransactionTimePeriod {
    return timeControlVM.selectedPeriod
  }

  /// 当前选择的日期（从时间控制器获取）
  var currentDate: Date {
    return timeControlVM.currentDate
  }

  /// 配置ViewModel的依赖
  ///
  /// 注入数据管理器，为ViewModel提供数据源。
  /// 这个方法应该在ViewModel创建后立即调用。
  ///
  /// - Parameter dataManager: 数据管理器，提供基础数据源
  ///
  /// - Important: 必须在使用ViewModel的其他方法之前调用此方法
  func configure(with dataManager: DataManagement) {
    self.dataManager = dataManager

    // 更新收入支出卡片的货币符号
    let baseCurrencyCode = CurrencyService.shared.baseCurrencyCode
    let currencySymbol = dataManager.currencies.first { $0.code == baseCurrencyCode }?.symbol ?? "¥"
    self.incomeExpenseCardVM.currencySymbol = currencySymbol
  }

  /// 安全地初始化当前卡片数据
  /// - Parameter card: 初始卡片数据
  func initializeCurrentCard(_ card: CardModel?) {
    guard let card = card else { return }

    // 尝试从dataManager获取最新数据，如果失败则使用传入的卡片
    if let latestCard = dataManager?.findCard(by: card.id) {
      self.currentCard = latestCard
    } else {
      self.currentCard = card
    }
  }

  // MARK: - 数据模型
  struct CardDetailData: Equatable, @unchecked Sendable {
    let cardId: UUID
    let income: Double
    let expense: Double
    let groupedTrandes:
      [(date: Date, transactions: [TransactionModel], income: Double, expense: Double)]
    let filteredTransactions: [TransactionModel]
    let dateRange: (start: Date, end: Date)
    let selectedPeriod: TransactionTimePeriod
    let currentDate: Date

    static func == (lhs: CardDetailData, rhs: CardDetailData) -> Bool {
      return lhs.cardId == rhs.cardId && lhs.income == rhs.income && lhs.expense == rhs.expense
        && lhs.selectedPeriod == rhs.selectedPeriod && lhs.currentDate == rhs.currentDate
    }
  }

  // MARK: - 枚举定义

  /// 编辑金额类型
  enum EditingAmount {
    case balance, debt, limit

    var title: String {
      switch self {
      case .balance: return "编辑余额"
      case .debt: return "编辑欠款"
      case .limit: return "编辑额度"
      }
    }
  }

  /// 日期字段类型
  enum DateField {
    case billing, repayment
  }

  /// 还款类型
  enum RepaymentType: String {
    case fixedDay = "固定日期"
    case afterBilling = "出账后"

    func displayText(day: Int?) -> String {
      guard let day = day else { return "未设置" }

      if day == 0 {
        switch self {
        case .fixedDay: return "每月最后一天"
        case .afterBilling: return "出账后0日"
        }
      }

      switch self {
      case .fixedDay: return "每月 \(day) 日"
      case .afterBilling: return "出账后 \(day) 日"
      }
    }
  }

  // MARK: - 计算属性

  /// 是否为信用卡类型
  func isCreditCard(isCreatingCard: Bool, isCredit: Bool?, card: CardModel?) -> Bool {
    isCreatingCard ? (isCredit ?? false) : (card?.isCredit ?? false)
  }

  /// 默认货币信息
  func defaultCurrency(baseCurrency: [CurrencyModel]) -> (code: String, symbol: String) {
    let code = baseCurrency.first?.code ?? "CNY"
    let symbol = baseCurrency.first?.symbol ?? "¥"
    return (code, symbol)
  }

  /// 当前货币代码
  func currentCurrencyCode(card: CardModel?, baseCurrency: [CurrencyModel]) -> String {
    editSelectedCurrencyCode.isEmpty
      ? (card?.currency ?? defaultCurrency(baseCurrency: baseCurrency).code)
      : editSelectedCurrencyCode
  }

  /// 当前货币符号
  func currentCurrencySymbol(card: CardModel?, baseCurrency: [CurrencyModel]) -> String {
    editCurrencySymbol.isEmpty
      ? (card?.symbol ?? defaultCurrency(baseCurrency: baseCurrency).symbol)
      : editCurrencySymbol
  }

  /// 卡片显示名称
  var displayCardName: String {
    editCardName.isEmpty ? "新建卡片" : editCardName
  }

  /// 分类名称
  func categoryName(subCategory: CardSubCategoryResponse?, mainCategory: CardCategoryResponse?)
    -> String
  {
    subCategory?.displayName ?? mainCategory?.name ?? "未分类"
  }

  /// 账单日显示值（用于UI显示）
  var billingDayForDisplay: Int {
    editBillingDay == 0 ? 31 : (editBillingDay ?? 1)
  }

  /// 账单日显示文本
  var billingDayDisplayText: String {
    editHasBillDaySet
      ? RepaymentType(rawValue: editRepaymentType.rawValue)?.displayText(day: editBillingDay)
        ?? "未设置" : "未设置"
  }

  /// 还款日显示文本
  var repaymentDayDisplayText: String {
    editHasDueDaySet
      ? RepaymentType(rawValue: editRepaymentType.rawValue)?.displayText(day: editRepaymentDay)
        ?? "未设置" : "未设置"
  }

  // MARK: - 公共方法

  /// 获取加载状态
  var isLoading: Bool {
    isLoadingCardDetail
  }

  /// 获取错误信息
  var errorMessage: String? {
    cardDetailErrorMessage
  }

  /// 准备显示数据（统一的数据准备入口）
  func prepareDataForDisplay(
    isCreatingCard: Bool,
    card: CardModel?,
    baseCurrency: [CurrencyModel],
    isCredit: Bool?,
    transactions: [TransactionModel],
    currencies: [CurrencyModel],
    selectedPeriod: TransactionTimePeriod,
    currentDate: Date
  ) {
    if isCreatingCard {
      // 创建模式：加载默认数据
      loadDefaultDataForCreation(baseCurrency: baseCurrency, isCredit: isCredit)
    } else {
      // 编辑模式：加载现有数据
      loadCardDetailData(
        card: card,
        selectedPeriod: selectedPeriod,
        currentDate: currentDate)
      loadExistingCardData(card: card)
    }
  }

  /// 加载卡片详情数据
  /// - Parameters:
  ///   - card: 卡片模型
  ///   - selectedPeriod: 选择的时间段
  ///   - currentDate: 当前日期
  func loadCardDetailData(
    card: CardModel?,
    selectedPeriod: TransactionTimePeriod,
    currentDate: Date
  ) {
    guard let card = card else {
      print("⚠️ CardDetailOverlayVM: loadCardDetailData called with nil card")
      return
    }

    // 确保使用最新的卡片数据
    if let latestCard = dataManager?.findCard(by: card.id) {
      self.currentCard = latestCard
    } else {
      self.currentCard = card
      print("⚠️ CardDetailOverlayVM: Could not find latest card data, using provided card")
    }

    isLoadingCardDetail = true
    cardDetailErrorMessage = nil

    // 从DataManagement获取数据
    let transactions = allTransactions
    let currencies = allCurrencies

    // 存储原始数据以供后续使用
    self.originalTransactions = transactions
    self.originalCurrencies = currencies

    // 使用最新的卡片数据创建副本
    guard let currentCard = self.currentCard else {
      print("❌ CardDetailOverlayVM: currentCard is nil after initialization")
      isLoadingCardDetail = false
      return
    }

    let cardCopy = CardModelData(from: currentCard)
    let transactionsCopy = transactions.map { TransactionModelData(from: $0) }
    let currenciesCopy = currencies.map { CurrencyModelData(from: $0) }

    Task {
      do {
        let data = try await processCardDetailDataAsync(
          card: cardCopy,
          transactions: transactionsCopy,
          currencies: currenciesCopy,
          selectedPeriod: selectedPeriod,
          currentDate: currentDate
        )

        await MainActor.run {
          // 再次验证卡片数据一致性
          guard let finalCard = self.currentCard else {
            print("❌ CardDetailOverlayVM: currentCard became nil during processing")
            self.isLoadingCardDetail = false
            return
          }

          self.cardDetailData = data
          // 同时更新收入支出卡片ViewModel
          self.incomeExpenseCardVM.income = data.income
          self.incomeExpenseCardVM.expense = data.expense
          self.incomeExpenseCardVM.currencySymbol = finalCard.symbol
          self.isLoadingCardDetail = false

          print("✅ CardDetailOverlayVM: Successfully loaded data for card: \(finalCard.name)")
          print(
            "📊 CardDetailData: income=\(data.income), expense=\(data.expense), transactions=\(data.groupedTrandes.count) groups"
          )
          print("📋 Grouped transactions details:")
          for (index, group) in data.groupedTrandes.enumerated() {
            print(
              "  Group \(index): date=\(group.date), transactions=\(group.transactions.count), income=\(group.income), expense=\(group.expense)"
            )
          }
        }
      } catch {
        await MainActor.run {
          self.cardDetailErrorMessage = "加载卡片详情失败: \(error.localizedDescription)"
          self.isLoadingCardDetail = false
          print("❌ CardDetailOverlayVM: Failed to load card detail data: \(error)")
        }
      }
    }
  }

  /// 是否为当前日期
  func isCurrentDate() -> Bool {
    Calendar.current.isDate(currentDate, inSameDayAs: Date())
  }

  // MARK: - 卡片操作方法

  /// 创建预览用的CardModel对象，基于编辑状态数据
  func createPreviewCard(
    card: CardModel?,
    isCreatingCard: Bool,
    isCredit: Bool?,
    subCategory: CardSubCategoryResponse?,
    mainCategory: CardCategoryResponse?,
    baseCurrency: [CurrencyModel]
  ) -> CardModel {
    let isCreditCard = self.isCreditCard(
      isCreatingCard: isCreatingCard, isCredit: isCredit, card: card)
    let currentCurrencyCode = self.currentCurrencyCode(card: card, baseCurrency: baseCurrency)
    let currentCurrencySymbol = self.currentCurrencySymbol(card: card, baseCurrency: baseCurrency)

    // 获取logo数据 - 创建模式使用类别图标，编辑模式保持原有图标
    let logoData: Data? = {
      if isCreatingCard {
        // 创建模式：使用类别图标
        if let subCategory = subCategory {
          return UIImage(named: subCategory.displayName)?.pngData()
        } else if let mainCategory = mainCategory {
          return UIImage(named: mainCategory.imageUrl)?.pngData()
        }
        return nil
      } else {
        // 编辑模式：保持原有图标
        return card?.bankLogo
      }
    }()

    let previewCard = CardModel(
      id: card?.id ?? UUID(),
      order: card?.order ?? 0,
      isCredit: isCreditCard,
      isSelected: true,
      name: displayCardName,
      remark: card?.remark ?? "",
      currency: currentCurrencyCode,
      symbol: currentCurrencySymbol,
      balance: editBalance,  // 信用卡和储蓄卡都直接使用余额
      credit: editLimit,
      isStatistics: false,
      cover: editSelectedcoverType.rawValue,
      bankLogo: logoData,
      bankName: subCategory?.displayName ?? mainCategory?.name ?? card?.bankName ?? "",
      cardNumber: card?.cardNumber ?? "",
      isFixedDueDay: true,
      createdAt: card?.createdAt ?? Date(),
      updatedAt: Date()
    )
    return previewCard
  }

  /// 取消卡片编辑
  func cancelCardEditing(isCreatingCard: Bool, card: CardModel?) {
    if isCreatingCard {
      // 创建模式下使用动画关闭视图
      // 这里需要在View中处理关闭逻辑
    } else {
      // 编辑模式下恢复原始数据，确保完全一致
      restoreOriginalCardData(card: card)

      // 退出编辑模式
      isEditingCard = false
    }
  }

  /// 处理数字键盘输入
  func handleNumericKeypadInput() {
    if let amount = Double(tempAmount) {
      switch editingAmount {
      case .balance:
        // 余额可以是正数（存款）或负数（欠款）
        editBalance = amount
      case .debt:
        // 不再使用欠款字段
        break
      case .limit:
        // 额度始终存储为正数
        editLimit = abs(amount)
      case .none:
        break
      }
    }
    showingNumericKeypad = false
  }

  /// 处理账单日保存
  func handleBillingDaySave() {
    editHasBillDaySet = true
    editBillingDay = selectedDay == 31 ? 0 : selectedDay
    showingBillingDayPicker = false
    focusedDateField = nil
  }

  /// 处理账单日清除
  func handleBillingDayClear() {
    editHasBillDaySet = false
    editBillingDay = nil
    showingBillingDayPicker = false
    focusedDateField = nil
  }

  /// 处理还款日保存
  func handleRepaymentDaySave() {
    editHasDueDaySet = true
    editRepaymentDay = (editRepaymentType == .fixedDay && selectedDay == 31) ? 0 : selectedDay
    showingRepaymentDayPicker = false
    focusedDateField = nil
  }

  /// 处理还款日清除
  func handleRepaymentDayClear() {
    editHasDueDaySet = false
    editRepaymentDay = nil
    showingRepaymentDayPicker = false
    focusedDateField = nil
  }

  /// 显示金额编辑键盘
  func showAmountEditKeypad(for amountType: EditingAmount, amount: Double) {
    editingAmount = amountType
    // 对于额度，显示时使用绝对值（确保显示为正数）
    // 对于余额，可以显示负数（信用卡欠款）
    let displayAmount = (amountType == .limit) ? abs(amount) : amount
    let maxDecimalPlaces = keypadMaxDecimalPlaces(for: amountType)
    tempAmount = NumberFormatService.shared.formatAmountForInput(
      displayAmount, maxDecimals: maxDecimalPlaces)
    showingNumericKeypad = true
  }

  /// 获取键盘配置：是否允许负数
  func keypadAllowsNegative(for amountType: EditingAmount) -> Bool {
    // 只有在编辑卡片余额时才允许负数（用于信用卡欠款）
    return amountType == .balance
  }

  /// 获取键盘配置：最大小数位数
  func keypadMaxDecimalPlaces(for amountType: EditingAmount) -> Int {
    // 所有金额类型都使用2位小数（汇率在其他地方处理）
    return 2
  }

  /// 显示账单日选择器
  func showBillingDayPicker() {
    selectedDay = billingDayForDisplay
    focusedDateField = .billing
    showingBillingDayPicker = true
  }

  /// 显示还款日选择器
  func showRepaymentDayPicker() {
    selectedDay = editRepaymentDay ?? 1
    focusedDateField = .repayment
    showingRepaymentDayPicker = true
  }

  /// 切换卡片翻转状态
  func toggleCardFlip() {
    withAnimation {
      isEditingCard = true
      isFlipped.toggle()
      rotationAngle = isFlipped ? 180 : 0
    }
  }

  /// 退出编辑模式
  func exitEditingMode() {
    withAnimation {
      isEditingCard = false
    }
  }

  /// 处理货币选择
  func handleCurrencySelection(code: String) {
    editSelectedCurrencyCode = code
  }

  /// 删除卡片（包含交易）
  func deleteCardWithTransactions(card: CardModel, modelContext: ModelContext) {
    do {
      // 1. 查找所有与该卡片相关的交易记录
      let descriptor = FetchDescriptor<TransactionModel>()
      let allTransactions = try modelContext.fetch(descriptor)

      // 使用 TransactionQueryService 查询卡片相关交易
      let relatedTransactions = allTransactions.filter { transaction in
        transaction.fromCardId == card.id || transaction.toCardId == card.id
      }

      // 2. 检查转账交易的完整性
      let problematicTransfers = checkTransferIntegrityForDeletion(
        cardToDelete: card,
        allTransactions: allTransactions,
        relatedTransactions: relatedTransactions
      )

      if !problematicTransfers.isEmpty {
        let transferIds = problematicTransfers.map { $0.id.uuidString.prefix(8) }.joined(
          separator: ", ")
        alertMessage =
          "无法删除卡片：存在 \(problematicTransfers.count) 个转账交易会因删除此卡片而变成无效状态。转账ID: \(transferIds)。请先删除这些转账交易。"
        showingAlert = true
        return
      }

      // 3. 删除所有相关的交易记录
      for transaction in relatedTransactions {
        modelContext.delete(transaction)
        print("[删除卡片] 删除相关交易: \(transaction.id), 类型: \(transaction.transactionType)")
      }

      // 4. 删除卡片本身
      modelContext.delete(card)

      // 5. 保存更改
      try modelContext.save()
      showCardActionSheet = false

      print("[删除卡片] 成功删除卡片 \(card.name) 及其 \(relatedTransactions.count) 个相关交易")

    } catch {
      alertMessage = "删除卡片失败：\(error.localizedDescription)"
      showingAlert = true
      print("[删除卡片] 删除失败: \(error.localizedDescription)")
    }
  }

  /// 删除卡片（仅卡片）
  func deleteCardOnly(card: CardModel, modelContext: ModelContext) {
    do {
      // 1. 查找所有与该卡片相关的交易记录
      let descriptor = FetchDescriptor<TransactionModel>()
      let allTransactions = try modelContext.fetch(descriptor)

      // 使用 TransactionQueryService 查询卡片相关交易
      let relatedTransactions = allTransactions.filter { transaction in
        transaction.fromCardId == card.id || transaction.toCardId == card.id
      }

      // 2. 找出需要删除的基准点交易（创建卡片和调整卡片）- 使用 TransactionQueryService
      let basePointTransactions = TransactionQueryService.shared.filterTransactions(
        relatedTransactions,
        byTypes: [.createCard, .adjustCard]
      )

      // 3. 删除基准点交易（创建卡片和调整卡片的交易）
      for transaction in basePointTransactions {
        modelContext.delete(transaction)
        print("[删除卡片] 删除基准点交易: \(transaction.id), 类型: \(transaction.transactionType)")
      }

      // 4. 删除卡片本身
      modelContext.delete(card)
      try modelContext.save()
      showCardActionSheet = false

      let deletedTransactionsCount = basePointTransactions.count
      let remainingTransactions = relatedTransactions.count - deletedTransactionsCount

      if deletedTransactionsCount > 0 {
        print("[删除卡片] 成功删除卡片 \(card.name) 及其 \(deletedTransactionsCount) 个基准点交易")
        if remainingTransactions > 0 {
          print("[删除卡片] 保留 \(remainingTransactions) 个其他类型交易（将成为孤儿交易）")
        }
      } else {
        print("[删除卡片] 成功删除卡片 \(card.name)（无基准点交易）")
        if remainingTransactions > 0 {
          print("[删除卡片] 保留 \(remainingTransactions) 个交易（将成为孤儿交易）")
        }
      }

    } catch {
      alertMessage = "删除卡片失败：\(error.localizedDescription)"
      showingAlert = true
      print("[删除卡片] 删除失败: \(error.localizedDescription)")
    }
  }

  // MARK: - Private Methods

  /// 异步处理卡片详情数据
  /// - Parameters:
  ///   - card: 卡片数据
  ///   - transactions: 交易数据
  ///   - currencies: 货币数据
  ///   - selectedPeriod: 选择的时间段
  ///   - currentDate: 当前日期
  /// - Returns: 处理后的卡片详情数据
  private func processCardDetailDataAsync(
    card: CardModelData,
    transactions: [TransactionModelData],
    currencies: [CurrencyModelData],
    selectedPeriod: TransactionTimePeriod,
    currentDate: Date
  ) async throws -> CardDetailData {
    return try await MainActor.run {
      return try self.processCardDetailDataOnMainThread(
        cardData: card,
        transactionsData: transactions,
        currenciesData: currencies,
        selectedPeriod: selectedPeriod,
        currentDate: currentDate
      )
    }
  }

  /// 在主线程上处理卡片详情数据
  /// - Parameters:
  ///   - cardData: 卡片数据
  ///   - transactionsData: 交易数据
  ///   - currenciesData: 货币数据
  ///   - selectedPeriod: 选择的时间段
  ///   - currentDate: 当前日期
  /// - Returns: 处理后的卡片详情数据
  @MainActor
  private func processCardDetailDataOnMainThread(
    cardData: CardModelData,
    transactionsData: [TransactionModelData],
    currenciesData: [CurrencyModelData],
    selectedPeriod: TransactionTimePeriod,
    currentDate: Date
  ) throws -> CardDetailData {
    // 计算日期范围
    let dateRange = calculateDateRange(for: selectedPeriod, currentDate: currentDate)

    // 使用存储的原始transactions数组
    let transactions = self.originalTransactions
    let currencies = self.originalCurrencies

    // 创建临时的CardModel对象用于计算
    let tempCard = createTempCardModel(from: cardData)

    // 过滤当前时间段的交易记录
    let filteredTransactions = transactions.filter { transaction in
      // 只统计与当前卡片相关的交易
      guard transaction.fromCardId == cardData.id || transaction.toCardId == cardData.id else {
        return false
      }
      // 时间范围过滤
      return transaction.transactionDate >= dateRange.start
        && transaction.transactionDate < dateRange.end
    }

    // 按日期分组交易
    let groupedTransactions = groupTransactionsByDate(
      transactions: filteredTransactions,
      card: tempCard,
      currencies: currencies
    )

    // 计算总收支
    let (totalIncome, totalExpense) = calculateTotalIncomeExpense(
      transactions: filteredTransactions,
      card: tempCard,
      currencies: currencies
    )

    return CardDetailData(
      cardId: cardData.id,
      income: totalIncome,
      expense: totalExpense,
      groupedTrandes: groupedTransactions,
      filteredTransactions: filteredTransactions,
      dateRange: dateRange,
      selectedPeriod: selectedPeriod,
      currentDate: currentDate
    )
  }

  /// 计算日期范围
  private nonisolated func calculateDateRange(for period: TransactionTimePeriod, currentDate: Date)
    -> (
      start: Date, end: Date
    )
  {
    let calendar = Calendar.current
    let now = currentDate

    switch period {
    case .week:
      let weekStart = calendar.date(
        from: calendar.dateComponents([.yearForWeekOfYear, .weekOfYear], from: now))!
      let weekEnd = calendar.date(byAdding: .weekOfYear, value: 1, to: weekStart)!
      return (weekStart, weekEnd)
    case .month:
      let components = calendar.dateComponents([.year, .month], from: now)
      let monthStart = calendar.date(from: components)!
      let monthEnd = calendar.date(byAdding: .month, value: 1, to: monthStart)!
      return (monthStart, monthEnd)
    case .year:
      let components = calendar.dateComponents([.year], from: now)
      let yearStart = calendar.date(from: components)!
      let yearEnd = calendar.date(byAdding: .year, value: 1, to: yearStart)!
      return (yearStart, yearEnd)
    }
  }

  /// 按日期分组交易
  private func groupTransactionsByDate(
    transactions: [TransactionModel],
    card: CardModel,
    currencies: [CurrencyModel]
  ) -> [(date: Date, transactions: [TransactionModel], income: Double, expense: Double)] {
    // 使用 TransactionQueryService 按日期分组
    let grouped = TransactionQueryService.shared.groupTransactionsByDate(transactions)

    return grouped.map { (date, transactions) in
      let dailySummary = calculateDailySummary(
        transactions: transactions,
        card: card,
        currencies: currencies
      )
      // 对同一天内的交易按时间降序排序（最新的在前面）
      let sortedTransactions = transactions.sorted { $0.transactionDate > $1.transactionDate }
      return (
        date: date,
        transactions: sortedTransactions,
        income: dailySummary.income,
        expense: dailySummary.expense
      )
    }.sorted { $0.date > $1.date }
  }

  /// 计算每日收支统计
  private func calculateDailySummary(
    transactions: [TransactionModel],
    card: CardModel,
    currencies: [CurrencyModel]
  ) -> (income: Double, expense: Double) {
    var income: Double = 0
    var expense: Double = 0

    for transaction in transactions {
      // 使用便捷方法转换交易金额到卡片货币
      let convertedAmount = CurrencyService.shared.convertTransactionAmountToTargetCurrency(
        transaction: transaction,
        targetCurrency: card.currency
      )

      switch transaction.transactionType {
      case .income:
        if transaction.toCardId == card.id {
          income += convertedAmount
        }
      case .expense:
        if transaction.fromCardId == card.id {
          expense += convertedAmount
        }
      case .transfer:
        if transaction.fromCardId == card.id {
          // 转出计入支出
          expense += convertedAmount
        } else if transaction.toCardId == card.id {
          // 转入计入收入
          income += convertedAmount
        }
      case .refund:
        if transaction.toCardId == card.id {
          income += convertedAmount
        }
      default:
        break
      }
    }

    return (income: income, expense: expense)
  }

  /// 计算总收支
  private func calculateTotalIncomeExpense(
    transactions: [TransactionModel],
    card: CardModel,
    currencies: [CurrencyModel]
  ) -> (income: Double, expense: Double) {
    var totalIncome: Double = 0
    var totalExpense: Double = 0

    for transaction in transactions {
      // 使用便捷方法转换交易金额到卡片货币
      let convertedAmount = CurrencyService.shared.convertTransactionAmountToTargetCurrency(
        transaction: transaction,
        targetCurrency: card.currency
      )

      switch transaction.transactionType {
      case .income:
        if transaction.toCardId == card.id {
          totalIncome += convertedAmount
        }
      case .expense:
        if transaction.fromCardId == card.id {
          totalExpense += convertedAmount
        }
      case .transfer:
        if transaction.fromCardId == card.id {
          // 转出计入支出
          totalExpense += convertedAmount
        } else if transaction.toCardId == card.id {
          // 转入计入收入
          totalIncome += convertedAmount
        }
      case .refund:
        if transaction.toCardId == card.id {
          totalIncome += convertedAmount
        }
      default:
        break
      }
    }

    return (income: totalIncome, expense: totalExpense)
  }

  /// 加载创建模式的默认数据
  func loadDefaultDataForCreation(baseCurrency: [CurrencyModel], isCredit: Bool?) {
    // 设置默认卡片名称为"新建卡片"（而不是空字符串）
    editCardName = "新建卡片"
    editIsIncludeInTotal = true
    editIsAvailableForBookkeeping = true

    // 设置默认货币
    let defaultCurrency = self.defaultCurrency(baseCurrency: baseCurrency)
    editSelectedCurrencyCode = defaultCurrency.code
    editCurrencySymbol = defaultCurrency.symbol

    // 设置默认背景
    editSelectedcoverType = .card1
    editisCoverDark = false

    // 根据资产类型初始化
    let isCreditCard = isCredit ?? false
    if !isCreditCard {
      editBalance = 0
    } else {
      initializeCreditCardDefaults()
    }
  }

  /// 初始化信用卡默认值
  private func initializeCreditCardDefaults() {
    editDebt = 0
    editLimit = 0
    editHasBillDaySet = false
    editHasDueDaySet = false
    editBillingDay = nil
    editRepaymentDay = nil
    editRepaymentType = .fixedDay
  }

  /// 加载现有卡片数据
  func loadExistingCardData(card: CardModel?) {
    guard let card = card else { return }

    // 基本信息
    editCardName = card.name
    editIsIncludeInTotal = card.isStatistics  // 使用isStatistics字段
    editIsAvailableForBookkeeping = card.isSelected
    editSelectedCurrencyCode = card.currency
    editCurrencySymbol = card.symbol

    // 背景设置 - 优化：一次性获取背景信息
    let coverType = CardCoverHelper.shared.getCoverType(from: card.cover)
    editSelectedcoverType = coverType
    editisCoverDark = CardCoverHelper.shared.isCoverDark(for: coverType)

    // 根据卡片类型加载特定数据
    if !card.isCredit {
      editBalance = card.balance
    } else {
      loadCreditCardData(from: card)
    }
  }

  /// 加载信用卡特有数据
  private func loadCreditCardData(from card: CardModel) {
    // 信用卡直接使用余额（可以是正数存款或负数欠款）
    editBalance = card.balance
    editDebt = 0  // 不再使用欠款字段
    editLimit = card.credit

    // 设置账单日
    editHasBillDaySet = card.billDay != nil
    editBillingDay = card.billDay

    // 设置还款日
    editHasDueDaySet = card.dueDay != nil
    editRepaymentDay = card.dueDay
    editRepaymentType = card.isFixedDueDay ? .fixedDay : .afterBilling
  }

  /// 恢复原始卡片数据，确保与原card完全一致
  private func restoreOriginalCardData(card: CardModel?) {
    guard let card = card else { return }

    // 完全恢复基本信息
    editCardName = card.name
    editIsIncludeInTotal = card.isStatistics  // 使用isStatistics字段
    editIsAvailableForBookkeeping = card.isSelected
    editSelectedCurrencyCode = card.currency
    editCurrencySymbol = card.symbol

    // 恢复背景设置 - 优化：一次性获取背景信息
    let coverType = CardCoverHelper.shared.getCoverType(from: card.cover)
    editSelectedcoverType = coverType
    editisCoverDark = CardCoverHelper.shared.isCoverDark(for: coverType)

    // 完全恢复金额信息
    if !card.isCredit {
      editBalance = card.balance
    } else {
      // 信用卡直接使用余额（可以是正数存款或负数欠款）
      editBalance = card.balance
      editDebt = 0  // 不再使用欠款字段
      editLimit = card.credit

      // 完全恢复信用卡日期设置
      editHasBillDaySet = card.billDay != nil
      editBillingDay = card.billDay
      editHasDueDaySet = card.dueDay != nil
      editRepaymentDay = card.dueDay
      editRepaymentType = card.isFixedDueDay ? .fixedDay : .afterBilling
    }
  }

  /// 获取两种货币之间的汇率
  private func getCurrencyRate(
    from sourceCurrency: String,
    to targetCurrency: String,
    currencies: [CurrencyModel]
  ) -> Double {
    // 使用CurrencyService的正确汇率计算方法
    return CurrencyService.shared.getCurrencyRate(
      from: sourceCurrency,
      to: targetCurrency,
      currencies: currencies
    )
  }

  /// 将金额从一种货币转换为另一种货币
  private func convertAmount(
    amount: Double,
    from sourceCurrency: String,
    to targetCurrency: String,
    currencies: [CurrencyModel]
  ) -> Double {
    let rate = getCurrencyRate(from: sourceCurrency, to: targetCurrency, currencies: currencies)
    return amount * rate
  }

  /// 判断交易是否为基准点(创建或调整类型)
  private func isBasePointTransaction(_ transaction: TransactionModel) -> Bool {
    return transaction.transactionType == .createCard || transaction.transactionType == .adjustCard
  }

  /// 找出指定日期之前的最近基准点交易
  private func findLastBasePoint(
    before date: Date,
    card: CardModel,
    transactions: [TransactionModel]
  ) -> TransactionModel? {
    // 筛选当前卡片的所有基准点交易
    let basePoints = transactions.filter {
      isBasePointTransaction($0) && ($0.fromCardId == card.id || $0.toCardId == card.id)
        && $0.transactionDate <= date
    }

    // 按日期降序排序，找出最近的基准点
    return basePoints.sorted { $0.transactionDate > $1.transactionDate }.first
  }

  /// 计算交易的影响金额(已转换为卡片货币)
  private func calculateTransactionImpact(_ transaction: TransactionModel, card: CardModel)
    -> Double
  {
    let type = transaction.transactionType
    let baseAmount = abs(transaction.transactionAmount)
    let discountAmount = transaction.discountAmount ?? 0

    // 如果是调整类型，直接返回调整金额
    if type == .adjustCard {
      return transaction.currency == card.currency
        ? transaction.transactionAmount
        : transaction.transactionAmount  // 暂时省略汇率转换
    }

    // 如果是创建类型，直接返回创建金额
    if type == .createCard {
      return transaction.currency == card.currency
        ? baseAmount : baseAmount  // 暂时省略汇率转换
    }

    // 计算其他交易类型的影响
    var impact: Double = 0

    switch type {
    case .expense:
      if transaction.fromCardId == card.id {
        // 计算实际支出金额（交易金额 - 优惠金额）
        let actualExpense = baseAmount - discountAmount
        let convertedAmount = CurrencyService.shared.convertAmountUsingTransactionRate(
          amount: actualExpense,
          transaction: transaction,
          targetCurrency: card.currency,
          forExpense: true
        )
        impact = -convertedAmount
      }
    case .income:
      if transaction.toCardId == card.id {
        // 计算实际收入金额（交易金额 - 优惠金额）
        let actualIncome = baseAmount - discountAmount
        let convertedAmount = CurrencyService.shared.convertAmountUsingTransactionRate(
          amount: actualIncome,
          transaction: transaction,
          targetCurrency: card.currency,
          forExpense: false
        )
        impact = convertedAmount
      }
    case .transfer:
      // 实际转账金额 = 交易金额 - 优惠金额（如果有的话）
      let actualTransferAmount = baseAmount - discountAmount

      if transaction.fromCardId == card.id {
        let convertedAmount = CurrencyService.shared.convertAmountUsingTransactionRate(
          amount: actualTransferAmount,
          transaction: transaction,
          targetCurrency: card.currency,
          forExpense: true
        )
        impact = -convertedAmount
      } else if transaction.toCardId == card.id {
        let convertedAmount = CurrencyService.shared.convertAmountUsingTransactionRate(
          amount: actualTransferAmount,
          transaction: transaction,
          targetCurrency: card.currency,
          forExpense: false
        )
        impact = convertedAmount
      }
    case .refund:
      if transaction.toCardId == card.id {
        // 退款交易：使用退款金额或交易金额
        let refundAmount = transaction.refundAmount ?? baseAmount
        let convertedAmount = CurrencyService.shared.convertAmountUsingTransactionRate(
          amount: refundAmount,
          transaction: transaction,
          targetCurrency: card.currency,
          forExpense: false
        )
        impact = convertedAmount
      }
    default:
      break
    }

    return impact
  }

  // 已废弃的私有方法已删除，现在统一使用 CurrencyService 中的便捷方法：
  // - convertTransactionAmountToTargetCurrency: 通用交易金额转换
  // - convertTransactionImpactAmount: 计算交易影响（自动处理优惠）
  // - convertSpecificAmountUsingTransactionRate: 转换指定金额

  // MARK: - Sendable数据处理方法

  /// 计算每日收支统计（Sendable版本）
  private nonisolated func calculateDailySummaryFromSendable(
    transactions: [TransactionModelData],
    card: CardModelData,
    currencies: [CurrencyModelData]
  ) -> (income: Double, expense: Double) {
    var income: Double = 0
    var expense: Double = 0

    for transaction in transactions {
      let actualAmount = abs(transaction.transactionAmount)

      switch transaction.transactionType {
      case .income:
        if transaction.toCardId == card.id {
          // 使用交易记录中的历史汇率
          let convertedAmount = convertAmountUsingTransactionRateFromSendable(
            amount: actualAmount,
            transaction: transaction,
            targetCurrency: card.currency,
            forExpense: false
          )
          income += convertedAmount
        }
      case .expense:
        if transaction.fromCardId == card.id {
          // 使用交易记录中的历史汇率
          let convertedAmount = convertAmountUsingTransactionRateFromSendable(
            amount: actualAmount,
            transaction: transaction,
            targetCurrency: card.currency,
            forExpense: true
          )
          expense += convertedAmount
        }
      case .transfer:
        if transaction.fromCardId == card.id {
          // 转出 - 使用支出汇率
          let convertedAmount = convertAmountUsingTransactionRateFromSendable(
            amount: actualAmount,
            transaction: transaction,
            targetCurrency: card.currency,
            forExpense: true
          )
          expense += convertedAmount
        } else if transaction.toCardId == card.id {
          // 转入 - 使用收入汇率
          let convertedAmount = convertAmountUsingTransactionRateFromSendable(
            amount: actualAmount,
            transaction: transaction,
            targetCurrency: card.currency,
            forExpense: false
          )
          income += convertedAmount
        }
      case .refund:
        if transaction.toCardId == card.id {
          // 退款 - 使用收入汇率
          let convertedAmount = convertAmountUsingTransactionRateFromSendable(
            amount: actualAmount,
            transaction: transaction,
            targetCurrency: card.currency,
            forExpense: false
          )
          income += convertedAmount
        }
      default:
        break
      }
    }

    return (income: income, expense: expense)
  }

  /// 将金额从一种货币转换为另一种货币（Sendable版本）
  private nonisolated func convertAmountFromSendable(
    amount: Double,
    from sourceCurrency: String,
    to targetCurrency: String,
    currencies: [CurrencyModelData]
  ) -> Double {
    let rate = getCurrencyRateFromSendable(
      from: sourceCurrency, to: targetCurrency, currencies: currencies)
    return amount * rate
  }

  /// 使用交易记录的历史汇率进行金额转换（Sendable版本）
  private nonisolated func convertAmountUsingTransactionRateFromSendable(
    amount: Double,
    transaction: TransactionModelData,
    targetCurrency: String,
    forExpense: Bool
  ) -> Double {
    // 如果交易货币与目标货币相同，无需转换
    if transaction.currency == targetCurrency {
      return amount
    }

    // 获取本位币代码
    let baseCurrencyCode = UserDefaults.standard.string(forKey: "baseCurrencyCode") ?? "CNY"

    // 根据目标货币和交易类型选择合适的汇率
    let rate: Double
    if targetCurrency == baseCurrencyCode {
      // 转换到本位币
      if forExpense {
        rate = transaction.expenseToBaseRate
      } else {
        rate = transaction.incomeToBaseRate
      }
    } else {
      // 转换到卡片货币
      if forExpense {
        rate = transaction.expenseToCardRate
      } else {
        rate = transaction.incomeToCardRate
      }
    }

    return amount * rate
  }

  /// 获取两种货币之间的汇率（Sendable版本）
  private nonisolated func getCurrencyRateFromSendable(
    from sourceCurrency: String,
    to targetCurrency: String,
    currencies: [CurrencyModelData]
  ) -> Double {
    // 如果源货币和目标货币相同，汇率为1
    if sourceCurrency == targetCurrency {
      return 1.0
    }

    // 查找源货币和目标货币的汇率
    guard let sourceRate = currencies.first(where: { $0.code == sourceCurrency })?.rate,
      let targetRate = currencies.first(where: { $0.code == targetCurrency })?.rate
    else {
      return 1.0
    }

    // 使用正确的汇率计算公式：源货币汇率 / 目标货币汇率
    return sourceRate / targetRate
  }

  /// 将TransactionModelData数组转换为TransactionModel数组
  private nonisolated func convertTransactionModelDataToTransactionModel(
    _ transactionDataArray: [TransactionModelData]
  )
    -> [TransactionModel]
  {
    // 注意：这里我们需要创建临时的TransactionModel对象
    // 由于TransactionModel可能包含复杂的关系和SwiftData属性，我们需要谨慎处理
    // 暂时返回空数组，需要进一步实现
    return []
  }

  /// 从CardModelData创建临时的CardModel对象
  @MainActor
  private func createTempCardModel(from cardData: CardModelData) -> CardModel {
    return CardModel(
      id: cardData.id,
      order: cardData.order,
      isCredit: cardData.isCredit,
      isSelected: cardData.isSelected,
      name: cardData.name,
      remark: cardData.remark,
      currency: cardData.currency,
      symbol: cardData.symbol,
      balance: cardData.balance,
      credit: cardData.credit,
      isStatistics: cardData.isStatistics,
      cover: cardData.cover,
      bankName: cardData.bankName,
      cardNumber: cardData.cardNumber,
      billDay: cardData.billDay,
      isFixedDueDay: cardData.isFixedDueDay,
      dueDay: cardData.dueDay,
      createdAt: cardData.createdAt,
      updatedAt: cardData.updatedAt
    )
  }

  // MARK: - 交易记录管理

  /// 为新创建的卡片创建初始交易记录
  private func createInitialTransaction(for card: CardModel, modelContext: ModelContext) {
    let initialTransaction = TransactionModel(
      id: UUID(),
      transactionType: .createCard,
      transactionCategoryId: "SYS_CREATE_CARD",
      toCardId: card.id,  // 创建类型交易使用toCardId
      transactionAmount: card.balance,
      currency: card.currency,
      symbol: card.symbol,
      expenseToCardRate: 1.0,
      expenseToBaseRate: 1.0,
      incomeToCardRate: 1.0,
      incomeToBaseRate: 1.0,
      isStatistics: card.isStatistics,
      remark: "创建卡片",
      transactionDate: card.createdAt,
      createdAt: Date(),
      updatedAt: Date()
    )

    do {
      modelContext.insert(initialTransaction)
      try modelContext.save()
      print("[交易创建] 成功为卡片 \(card.name) 创建初始交易记录")
    } catch {
      print("[交易创建] 创建初始交易记录失败: \(error.localizedDescription)")
    }
  }

  /// 创建调整交易记录（当卡片余额发生变化时）
  private func createAdjustmentTransaction(
    card: CardModel,
    amount: Double,
    currencyCode: String,
    modelContext: ModelContext
  ) {
    let transaction = TransactionModel(
      id: UUID(),
      transactionType: .adjustCard,
      transactionCategoryId: "SYS_ADJUST_CARD",
      toCardId: card.id,  // 调整类型交易也使用toCardId
      transactionAmount: amount,
      currency: currencyCode,
      symbol: editCurrencySymbol,
      expenseToCardRate: 1.0,
      expenseToBaseRate: 1.0,
      incomeToCardRate: 1.0,
      incomeToBaseRate: 1.0,
      isStatistics: true,
      remark: "余额调整",
      transactionDate: Date(),
      createdAt: Date(),
      updatedAt: Date()
    )

    do {
      modelContext.insert(transaction)
      try modelContext.save()
      print("[交易创建] 成功为卡片 \(card.name) 创建调整交易记录")
    } catch {
      print("[交易创建] 创建调整交易记录失败: \(error.localizedDescription)")
    }
  }

  /// 检查删除卡片时转账交易的完整性
  /// - Parameters:
  ///   - cardToDelete: 要删除的卡片
  ///   - allTransactions: 所有交易记录
  ///   - relatedTransactions: 与该卡片相关的交易记录
  /// - Returns: 会变成无效状态的转账交易列表
  private func checkTransferIntegrityForDeletion(
    cardToDelete: CardModel,
    allTransactions: [TransactionModel],
    relatedTransactions: [TransactionModel]
  ) -> [TransactionModel] {
    var problematicTransfers: [TransactionModel] = []

    // 查找所有转账交易 - 使用 TransactionQueryService
    let transferTransactions = TransactionQueryService.shared.filterTransactions(
      allTransactions,
      byTypes: [.transfer]
    )

    for transfer in transferTransactions {
      // 检查这个转账是否涉及要删除的卡片
      let involvesCardToDelete =
        transfer.fromCardId == cardToDelete.id || transfer.toCardId == cardToDelete.id

      if involvesCardToDelete {
        // 检查转账的另一方卡片是否仍然存在
        let otherCardId: UUID?
        if transfer.fromCardId == cardToDelete.id {
          otherCardId = transfer.toCardId
        } else {
          otherCardId = transfer.fromCardId
        }

        // 如果另一方卡片存在，那么删除当前卡片会导致转账变成无效状态
        if otherCardId != nil {
          // 这里我们假设其他卡片仍然存在（因为我们只是在删除一个卡片）
          // 所以这个转账会变成问题转账
          problematicTransfers.append(transfer)
        }
      }
    }

    return problematicTransfers
  }
}
